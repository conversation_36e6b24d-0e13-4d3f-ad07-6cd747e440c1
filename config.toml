# 抖音数据抓取器配置文件

[douyin_id]
# 要爬取的抖音id
douyin_id = "96967475948"

[scraper]
# JavaScript 执行超时时间（秒）
js_timeout = 10
# 超时或返回空时的自动重试次数
js_retry = 3
# 单页内部重试的间隔（秒）
sleep_between_tries = 0.8
# 翻页间隔，降低风控概率（秒）
sleep_between_pages = 1.5
# 视频信息获取首次重试延迟（秒）
video_retry_base_delay = 0.8
# 视频信息获取后续重试额外延迟范围（秒）
video_retry_extra_delay_min = 1
video_retry_extra_delay_max = 2

[limits]
# 最大粉丝抓取数量
max_follower_count = 30
# 最大喜欢视频抓取数量
max_favorite_count = 20

[data_quality]
# 监听器队列清理超时时间（秒）
listener_cleanup_timeout = 1.0
# 监听器队列清理轮数
listener_cleanup_rounds = 3
# 是否启用严格的数据验证（验证视频ID一致性）
strict_data_validation = true
# 是否启用去重功能
enable_deduplication = true

[optimization]
# 性能优化阶段选择 (1: 监听器复用, 2: +队列清理, 3: +加载模式优化)
optimization_stage = 3
# 是否启用性能统计
enable_performance_stats = true
# 是否启用详细的调试日志
enable_debug_logging = false
# 批处理大小（用于未来的批量优化）
batch_size = 10

[concurrent]
# 是否启用并发模式（多标签页同时获取视频信息）
enable_concurrent_mode = true
# 最大并发标签页数（建议2-4个，过多可能触发风控）
max_concurrent_tabs = 3
# 标签页初始化间隔（秒）- 避免同时创建多个标签页触发风控
tab_init_delay = 2.0
# 并发批处理大小（每次分配给线程池的任务数量）
concurrent_batch_size = 10
# 并发任务失败重试次数
concurrent_retry_count = 2
# 并发模式下是否启用详细日志
concurrent_debug_logging = false

[export]
# 是否导出粉丝信息到 JSON 文件
follower_json = true
# 是否导出粉丝信息到 CSV 文件
follower_csv = true
# 是否导出喜欢视频信息到 JSON 文件
favorite_json = true
# 是否导出喜欢视频信息到 CSV 文件
favorite_csv = true
