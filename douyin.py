from DrissionPage import Chromium, ChromiumOptions
import jmespath
import json
from urllib.parse import urlsplit, parse_qsl, urlencode, urlunsplit
import time
import csv
import logging
import re
import random
from typing import List, Dict, Set, Union
from logger import setup_logger
from utils import load_config
import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import deque
import queue

class DouyinScraper:
    """抖音网页数据抓取器，基于 DrissionPage。"""

    # ---- 全局运行参数（按需修改） ----
    JS_TIMEOUT = 10                # 每次 run_js 的超时时间（秒）- 从3秒增加到10秒，减少误判超时
    JS_RETRY = 3                   # 超时或返回空时的自动重试次数（每个阶段）- 从2次增加到3次，提高成功率
    SLEEP_BETWEEN_TRIES = 0.8      # 单页内部重试的间隔（秒）
    SLEEP_BETWEEN_PAGES = 1.5      # 翻页间隔，降低风控概率
    VIDEO_RETRY_BASE_DELAY = 0.8   # 视频信息获取首次重试延迟（秒）
    VIDEO_RETRY_EXTRA_DELAY_MIN = 0.5  # 视频信息获取后续重试额外延迟最小值（秒）
    VIDEO_RETRY_EXTRA_DELAY_MAX = 0.8  # 视频信息获取后续重试额外延迟最大值（秒）

    def __init__(self):
        """初始化 Chromium 实例并配置个人数据目录.

        Args:
            user_data_path: 浏览器用户数据路径（新目录，避免历史缓存干扰）
        """
        options = ChromiumOptions()
        # options.no_imgs(True)
        options.set_user_data_path(r'D:\temp\dp_profile_clean')
        options.set_load_mode('none').mute(True)
        options.set_argument('--disable-gpu')   # 禁用GPU加速
        options.set_argument('--disable-dev-shm-usage')  # 减少内存使用
        options.set_argument('--disable-extensions')     # 禁用扩展
        options.set_argument('--disable-plugins')        # 禁用插件
        # options.set_user_data_path(user_data_path)
        # self.dp = Chromium(options).latest_tab
        self.dp = Chromium(options).latest_tab
        self.logger = setup_logger("DouyinScraper")

        # 加载配置并更新类变量
        self.config = load_config()
        self._update_config()

    def _update_config(self):
        """
        从配置文件更新类变量，保持向后兼容。
        """
        scraper_config = self.config.get('scraper', {})
        self.JS_TIMEOUT = scraper_config.get('js_timeout', self.JS_TIMEOUT)
        self.JS_RETRY = scraper_config.get('js_retry', self.JS_RETRY)
        self.SLEEP_BETWEEN_TRIES = scraper_config.get('sleep_between_tries', self.SLEEP_BETWEEN_TRIES)
        self.SLEEP_BETWEEN_PAGES = scraper_config.get('sleep_between_pages', self.SLEEP_BETWEEN_PAGES)
        self.VIDEO_RETRY_BASE_DELAY = scraper_config.get('video_retry_base_delay', self.VIDEO_RETRY_BASE_DELAY)
        self.VIDEO_RETRY_EXTRA_DELAY_MIN = scraper_config.get('video_retry_extra_delay_min', self.VIDEO_RETRY_EXTRA_DELAY_MIN)
        self.VIDEO_RETRY_EXTRA_DELAY_MAX = scraper_config.get('video_retry_extra_delay_max', self.VIDEO_RETRY_EXTRA_DELAY_MAX)

        # 数据质量配置
        data_quality_config = self.config.get('data_quality', {})
        self.LISTENER_CLEANUP_TIMEOUT = data_quality_config.get('listener_cleanup_timeout', 1.0)
        self.LISTENER_CLEANUP_ROUNDS = data_quality_config.get('listener_cleanup_rounds', 3)
        self.STRICT_DATA_VALIDATION = data_quality_config.get('strict_data_validation', True)
        self.ENABLE_DEDUPLICATION = data_quality_config.get('enable_deduplication', True)

        # 优化配置
        optimization_config = self.config.get('optimization', {})
        self.OPTIMIZATION_STAGE = optimization_config.get('optimization_stage', 3)
        self.ENABLE_PERFORMANCE_STATS = optimization_config.get('enable_performance_stats', True)
        self.ENABLE_DEBUG_LOGGING = optimization_config.get('enable_debug_logging', False)
        self.BATCH_SIZE = optimization_config.get('batch_size', 10)

        # 并发配置
        concurrent_config = self.config.get('concurrent', {})
        self.ENABLE_CONCURRENT_MODE = concurrent_config.get('enable_concurrent_mode', True)
        self.MAX_CONCURRENT_TABS = concurrent_config.get('max_concurrent_tabs', 3)
        self.TAB_INIT_DELAY = concurrent_config.get('tab_init_delay', 2.0)
        self.CONCURRENT_BATCH_SIZE = concurrent_config.get('concurrent_batch_size', 10)
        self.CONCURRENT_RETRY_COUNT = concurrent_config.get('concurrent_retry_count', 2)
        self.CONCURRENT_DEBUG_LOGGING = concurrent_config.get('concurrent_debug_logging', False)

    def _setup_listener_and_get(self, endpoint: str, url: str, expected_field: str = None):
        """
        统一的监听器设置和数据获取逻辑。

        Args:
            endpoint (str): 要监听的API端点
            url (str): 要访问的页面URL
            expected_field (str, optional): 期望在响应中存在的字段名

        Returns:
            tuple: (request_url, response_data) 或 (None, None) 如果失败
        """
        self.dp.listen.clear()
        self.dp.listen.start(endpoint)
        self.dp.get(url)

        for retry_count in range(self.JS_RETRY):
            try:
                pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                data = self._to_json(pkt.response.body)

                # 如果指定了期望字段，检查是否存在
                if expected_field and not data.get(expected_field):
                    self.logger.warning(f"响应中无 {expected_field} 字段, 重试: {retry_count + 1}")
                    if retry_count < self.JS_RETRY - 1:
                        time.sleep(self.SLEEP_BETWEEN_TRIES)
                    continue

                return pkt.request.url, data

            except Exception as e:
                self.logger.warning(f"获取响应异常: {e}, 重试: {retry_count + 1}")
                if retry_count < self.JS_RETRY - 1:
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

        return None, None

    def _get_video_retry_delay(self, retry_count: int) -> float:
        """
        计算视频信息获取的重试延迟时间。

        Args:
            retry_count (int): 当前重试次数（从0开始）

        Returns:
            float: 延迟时间（秒）
        """
        if retry_count == 0:
            # 第一次重试使用基础延迟
            return self.VIDEO_RETRY_BASE_DELAY
        else:
            # 后续重试在基础延迟基础上添加随机延迟
            extra_delay = random.uniform(self.VIDEO_RETRY_EXTRA_DELAY_MIN, self.VIDEO_RETRY_EXTRA_DELAY_MAX)
            total_delay = self.VIDEO_RETRY_BASE_DELAY + extra_delay
            return total_delay

    # ---------------- 工具方法 ----------------

    def _to_json(self, body) -> dict:
        """将 response.body 统一转换为 dict，增加异常处理避免JSON解析错误导致程序崩溃."""
        if not body:
            return {}

        try:
            if isinstance(body, (bytes, bytearray)):
                return json.loads(body.decode('utf-8', 'ignore'))
            if isinstance(body, str):
                return json.loads(body)
            return body or {}
        except json.JSONDecodeError as e:
            # JSON解析错误不应该导致程序崩溃，记录警告并返回空字典
            self.logger.warning(f"JSON解析失败: {e}")
            return {}
        except Exception as e:
            # 其他异常也进行处理
            self.logger.error(f"数据处理异常: {e}")
            return {}

    @staticmethod
    def _set_query_params(url: str, **kw) -> str:
        """在 URL 上修改或添加查询参数."""
        u = urlsplit(url)
        q = dict(parse_qsl(u.query, keep_blank_values=True))
        for k, v in kw.items():
            if v is not None:
                q[k] = str(v)
        return urlunsplit((u.scheme, u.netloc, u.path, urlencode(q, doseq=True), u.fragment))

    def _classify_error(self, error_msg: str, data: dict = None) -> str:
        """错误分类，避免将技术错误误判为滑块验证"""
        error_str = str(error_msg).lower()

        if "expecting value" in error_str or "json" in error_str:
            return "json_parse_error"    # JSON解析错误
        elif "timeout" in error_str:
            return "timeout_error"       # 超时错误
        elif "captcha" in error_str or "验证" in error_str:
            return "captcha_required"    # 滑块验证
        elif data is None or (isinstance(data, dict) and not data):
            return "empty_response"      # 空响应
        else:
            return "unknown_error"       # 未知错误

    def _should_trigger_captcha(self, data: dict, error_msg: str = "") -> bool:
        """判断是否真的需要滑块验证，避免将技术错误误判为人工验证需求"""
        error_type = self._classify_error(error_msg, data)

        # JSON解析错误、超时错误不是滑块问题
        if error_type in ["json_parse_error", "timeout_error"]:
            return False

        # 明确的验证需求
        if error_type == "captcha_required":
            return True

        # 空响应可能需要验证，但要更谨慎
        if error_type == "empty_response":
            return not data  # 只有在确实没有数据时才触发

        return False  # 其他情况不触发验证

    def _wait_user_to_solve(self, scene: str = '操作'):
        """在终端提示用户去浏览器完成滑块/安全验证，完成后回车继续。"""
        self.logger.warning(f"触发滑块/安全验证 - 场景: {scene}")
        print(f"\n[提示] 可能触发了滑块/安全验证（{scene}）。")
        print("        请切换到已打开的浏览器页面完成验证。")
        print("        完成后回到本窗口，按 Enter 继续；输入 q 然后回车可终止。\n")
        ack = input("完成验证后按 Enter 继续（或输入 q 退出）：").strip().lower()
        if ack == 'q':
            self.logger.info("用户选择退出任务")
            raise RuntimeError("用户取消，终止当前任务。")
        self.logger.info(f"用户完成验证，继续执行 - 场景: {scene}")

    def _fetch_json_via_js(self, url: str, scene: str) -> dict | None:
        """统一的 JS 拉取包装：任何 JS 超时/空返回都按触发滑块处理，并能在完成验证后继续原进度。

        流程：
            - 尝试 JS_RETRY 次，run_js 超时时间 JS_TIMEOUT；
            - 仍失败 → 提示用户过滑块；
            - 再尝试 JS_RETRY 次；
            - 若还失败，则抛错。
        """
        js = f"""
return (async () => {{
  const r = await fetch('{url}', {{ credentials: 'include' }});
  try {{ return await r.json(); }} catch (e) {{ return null; }}
}})()
"""

        # 阶段 1：自动重试
        for _ in range(self.JS_RETRY):
            try:
                data = self.dp.run_js(js, timeout=self.JS_TIMEOUT)
            except Exception:
                data = None
            if data:
                return data
            time.sleep(self.SLEEP_BETWEEN_TRIES)

        # 视为滑块
        self._wait_user_to_solve(scene)

        # 阶段 2：完成验证后再次重试
        for _ in range(self.JS_RETRY):
            try:
                data = self.dp.run_js(js, timeout=self.JS_TIMEOUT)
            except Exception:
                data = None
            if data:
                return data
            time.sleep(self.SLEEP_BETWEEN_TRIES)

        raise RuntimeError(f"多次尝试后仍未获取到数据（{scene}）。")

    # ---------------- 业务方法 ----------------

    def fetch_sec_uid(self, douyin_id: str) -> str:
        """通过搜索建议接口获取用户的 sec_uid。"""
        self.logger.info(f"开始获取用户 sec_uid - 抖音ID: {douyin_id}")

        endpoint = f"/aweme/v1/web/api/suggest_words/?query={douyin_id}"
        url = f'https://www.douyin.com/search/{douyin_id}'

        try:
            _, data = self._setup_listener_and_get(endpoint, url)

            if not data:
                self.logger.error(f"获取搜索建议失败 - 抖音ID: {douyin_id}")
                raise RuntimeError("获取搜索建议失败")

            msg = jmespath.search('data[0].params.extra_info.msg', data)
            payload = json.loads(msg) if msg else {}
            sec_uid = jmespath.search('src_query.sug_sec_uid', payload)

            if not sec_uid:
                self.logger.error(f"未找到 sec_uid - 抖音ID: {douyin_id}")
                raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")

            self.logger.info(f"成功获取 sec_uid: {sec_uid} - 抖音ID: {douyin_id}")
            return sec_uid

        finally:
            self.dp.listen.pause(True)

    def fetch_user_profile(self, sec_uid: str) -> dict:
        """获取用户基本信息."""
        endpoint = "/aweme/v1/web/user/profile/other/?device_platform=webapp"
        url = f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main'

        try:
            _, data = self._setup_listener_and_get(endpoint, url, 'user')

            if not data:
                self.logger.error(f"获取用户资料失败 - sec_uid: {sec_uid}")
                raise RuntimeError("获取用户资料失败")

            profile = data.get('user', {}) or {}
            return {
                'nickname': profile.get('nickname'),
                'uid': profile.get('uid'),
                'unique_id': profile.get('unique_id'),
                'followers': profile.get('mplatform_followers_count'),
                'following': profile.get('following_count'),
                'signature': profile.get('signature'),
                'aweme_count': profile.get('aweme_count'),
                'favoriting_count': profile.get('favoriting_count'),
            }
        finally:
            self.dp.listen.pause(True)

    def fetch_followers(self,
                        sec_uid: str,
                        max_items: int = 5000,
                        page_count: int = 20) -> List[Dict]:
        """抓取用户粉丝列表（PC Web），时间游标分页。"""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/user/follower/list/")

        # 进入主页 -> 点击“粉丝”，触发首包
        self.dp.get(f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main")
        locator = 'x://div[@data-e2e="user-info-fans"]'
        self.dp.wait.ele_displayed(locator, timeout=15)
        self.dp.ele(locator).click()

        try:
            # ---- 首包 ----
            first_req_url, first_data = None, None
            for retry_count in range(self.JS_RETRY):
                try:
                    # 使用推荐的 listen.wait() API
                    pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                    first_req_url = pkt.request.url
                    first_data = self._to_json(pkt.response.body)
                    if (first_data or {}).get('followers'):
                        break
                    else:
                        self.logger.warning(f"首包响应中无 followers 字段, 重试: {retry_count + 1}")
                except Exception as e:
                    self.logger.warning(f"获取粉丝首包异常: {e}, 重试: {retry_count + 1}")
                    first_data = None

                if retry_count < self.JS_RETRY - 1:
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((first_data or {}).get('followers')):
                self._wait_user_to_solve('获取粉丝列表（首包）')
                for retry_count in range(self.JS_RETRY):
                    self.dp.refresh()
                    try:
                        # 使用推荐的 listen.wait() API
                        pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                        first_req_url = pkt.request.url
                        first_data = self._to_json(pkt.response.body)
                        if (first_data or {}).get('followers'):
                            break
                        else:
                            self.logger.warning(f"验证后首包响应中仍无 followers 字段, 重试: {retry_count + 1}")
                    except Exception as e:
                        self.logger.warning(f"验证后获取粉丝首包异常: {e}, 重试: {retry_count + 1}")
                        first_data = None

                    if retry_count < self.JS_RETRY - 1:
                        time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((first_data or {}).get('followers')):
                raise RuntimeError("多次尝试后仍未获取到粉丝首包数据，请稍后再试。")

            # ---- 收集与去重 ----
            collected: List[Dict] = []
            seen: Set[str] = set()

            def _extend(items: List[Dict]) -> int:
                added = 0
                for it in items or []:
                    k = str(it.get('uid') or it.get('sec_uid') or it.get('unique_id') or id(it))
                    if k in seen:
                        continue
                    seen.add(k)
                    collected.append(it)
                    added += 1
                return added

            # ---- 处理首包 ----
            page_followers = first_data.get('followers') or []
            added = _extend(page_followers)
            has_more = bool(first_data.get('has_more'))
            next_max_time = int(first_data.get('min_time') or 0)
            last_min_time = next_max_time
            stuck_rounds = 0

            print(f"[首包] items={len(page_followers)} (+{added}) "
                  f"has_more={has_more} min_time={first_data.get('min_time')} "
                  f"max_time={first_data.get('max_time')} total={first_data.get('total')}")

            # ---- 翻页循环 ----
            while has_more and len(collected) < max_items:
                query_url = self._set_query_params(
                    first_req_url,
                    max_time=max(0, next_max_time),
                    count=page_count
                )

                page = self._fetch_json_via_js(query_url, scene='获取粉丝列表（翻页）')
                items = (page or {}).get('followers') or []
                page_added = _extend(items)
                has_more = bool((page or {}).get('has_more'))

                print(f"[粉丝翻页] items={len(items)} (+{page_added}) "
                      f"has_more={has_more} min_time={(page or {}).get('min_time')} "
                      f"max_time={(page or {}).get('max_time')} 粉丝数量累积={len(collected)}")
                if not has_more:
                    break

                page_min_time = int((page or {}).get('min_time') or 0)

                # 游标卡住 -> -1 纠偏；连卡两次退出
                if page_min_time >= last_min_time:
                    stuck_rounds += 1
                    next_max_time = max(0, last_min_time - 1)
                    if stuck_rounds >= 2:
                        print("[提示] 时间游标连续未推进，认为已到尽头，提前结束以避免死循环。")
                        break
                else:
                    next_max_time = page_min_time
                    last_min_time = page_min_time
                    stuck_rounds = 0

                time.sleep(self.SLEEP_BETWEEN_PAGES)
            follower_list_json = json.dumps(collected, ensure_ascii=False, indent=2)
            # 处理json数据,只保留特定字段
            follower_list = json.loads(follower_list_json)

            # 定义需要保留的字段（支持嵌套字段）
            keep_fields = {
                '用户UID': 'uid',
                '用户sec_uid': 'sec_uid',
                '用户抖音号': 'unique_id',
                '用户昵称': 'nickname',
                '用户签名': 'signature',
                '用户头像': 'avatar_thumb.url_list[0]',
                '粉丝数': 'mplatform_followers_count',
                '关注数': 'following_count',
                '作品数': 'aweme_count',
                '获赞数': 'favoriting_count',
                '总被赞数': 'total_favorited',
                '是否官方账号': 'is_gov_media_vip',
                '是否为明星': 'is_star',
                '是否认证': 'is_verified',
            }

            def get_nested_value(data, path, default=''):
                """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
                try:
                    current = data
                    # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
                    import re
                    parts = re.split(r'[\.\[\]]', path)
                    parts = [p for p in parts if p]  # 移除空字符串

                    for part in parts:
                        if part.isdigit():  # 数组索引
                            current = current[int(part)]
                        else:  # 字典键
                            current = current[part]
                    return current if current is not None else default
                except (KeyError, TypeError, AttributeError, IndexError):
                    return default

            # 只保留指定字段，创建新的清理后的列表
            cleaned_followers = []
            for i, follower in enumerate(follower_list):
                # 调试：打印前3个粉丝的原始数据结构
                if i < 3:
                    self.logger.info(f"=== 粉丝 {i+1} 原始数据结构 ===")
                    self.logger.info(f"所有字段: {list(follower.keys())}")
                    # 检查关键字段
                    key_fields = ['unique_id', 'signature', 'follower_count', 'mplatform_followers_count', 'followers_count']
                    for field in key_fields:
                        if field in follower:
                            self.logger.info(f"{field}: {repr(follower[field])}")
                        else:
                            self.logger.info(f"{field}: 字段不存在")
                    self.logger.info("=" * 50)

                cleaned_follower = {}
                for new_field, path in keep_fields.items():
                    cleaned_follower[new_field] = get_nested_value(follower, path)
                cleaned_followers.append(cleaned_follower)
            

            return cleaned_followers[:max_items]

        finally:
            self.dp.listen.pause(True)

    def fetch_video_info(self,
                        video_id: str) -> Dict:
        """抓取视频信息。"""
        self.logger.debug(f"开始获取视频详情 - ID: {video_id}")

        # 性能瓶颈：每个视频都重新设置监听器 (耗时1-2秒)
        # 优化建议：改为一次性设置监听，在批处理开始前设置，多个视频复用
        # 推荐模式：在 fetch_favorites() 开始时设置一次监听即可
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")
        video_url = f"https://www.douyin.com/video/{video_id}"

        try:
            # 打开视频页面 → 获取首包数据
            self.logger.debug(f"访问视频页面: {video_url}")
            self.dp.get(video_url)

            # ---- 首包数据获取 ----
            data = None
            last_error = ""
            # 使用推荐的 listen.wait() API，内置超时处理和异常管理
            for retry_count in range(self.JS_RETRY):
                try:
                    pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                    response_size = len(pkt.response.body) if pkt.response.body else 0
                    self.logger.debug(f"获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_detail'):
                        self.logger.debug(f"成功获取视频详情数据 - ID: {video_id}")
                        break
                    else:
                        self.logger.warning(f"响应中无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
                except Exception as e:
                    # 记录最后一次错误，用于后续错误分类
                    last_error = str(e)
                    error_type = self._classify_error(last_error, data)
                    self.logger.warning(f"获取响应包异常 - ID: {video_id}, 错误类型: {error_type}, 错误: {e}, 重试: {retry_count + 1}")

                if retry_count < self.JS_RETRY - 1:
                    delay = self._get_video_retry_delay(retry_count)
                    self.logger.debug(f"视频重试延迟: {delay:.2f}s - ID: {video_id}, 重试次数: {retry_count + 1}")
                    time.sleep(delay)

            # 改进的错误判断逻辑：区分JSON解析错误和真正的滑块验证需求
            # 只有在确实需要滑块验证时才触发人工验证
            if not ((data or {}).get('aweme_detail')):
                if self._should_trigger_captcha(data, last_error):
                    self.logger.warning(f"检测到需要滑块验证 - ID: {video_id}")
                    self._wait_user_to_solve('获取视频信息（首包）')

                    for retry_count in range(self.JS_RETRY):
                        self.dp.refresh()
                        try:
                            pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                            response_size = len(pkt.response.body) if pkt.response.body else 0
                            self.logger.debug(f"验证后获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                            data = self._to_json(pkt.response.body)
                            if (data or {}).get('aweme_detail'):
                                self.logger.info(f"验证后成功获取视频详情数据 - ID: {video_id}")
                                break
                            else:
                                self.logger.warning(f"验证后响应中仍无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
                        except Exception as e:
                            self.logger.warning(f"验证后获取响应包异常 - ID: {video_id}, 错误: {e}, 重试: {retry_count + 1}")

                        if retry_count < self.JS_RETRY - 1:
                            delay = self._get_video_retry_delay(retry_count)
                            self.logger.debug(f"验证后视频重试延迟: {delay:.2f}s - ID: {video_id}, 重试次数: {retry_count + 1}")
                            time.sleep(delay)
                else:
                    # 技术错误，不需要人工验证，直接记录并继续
                    error_type = self._classify_error(last_error, data)
                    self.logger.info(f"检测到技术错误({error_type})，跳过滑块验证 - ID: {video_id}")

            video_detail = (data or {}).get('aweme_detail')
            if not video_detail:
                self.logger.error(f"多次尝试后仍未获取到视频详情数据 - ID: {video_id}")
                raise RuntimeError("多次尝试后仍未获取到视频详情数据，请稍后再试。")

            # 使用统一的视频详情处理方法
            return self._process_video_detail(video_detail, video_id)

        finally:
            self.dp.listen.pause(True)

    def _fetch_video_info_optimized(self, video_id: str) -> Dict:
        """优化版本的视频信息获取，复用已设置的监听器，避免重复设置监听器的性能开销"""
        self.logger.debug(f"开始获取视频详情（优化版本） - ID: {video_id}")

        video_url = f"https://www.douyin.com/video/{video_id}"

        # 关键修复：清理监听器队列中的旧数据包，防止数据混乱
        self.logger.debug(f"清理监听器队列 - ID: {video_id}")
        cleaned_packets = 0

        # 多轮清理，确保队列完全清空
        for cleanup_round in range(self.LISTENER_CLEANUP_ROUNDS):
            round_cleaned = 0
            try:
                # 清空监听器中的旧数据包
                while True:
                    try:
                        old_pkt = self.dp.listen.wait(timeout=self.LISTENER_CLEANUP_TIMEOUT, raise_err=True)
                        round_cleaned += 1
                        cleaned_packets += 1
                        # 记录被清理的数据包信息，便于调试
                        if old_pkt and hasattr(old_pkt, 'response') and old_pkt.response:
                            response_size = len(old_pkt.response.body) if old_pkt.response.body else 0
                            self.logger.debug(f"清理旧数据包 - 轮次: {cleanup_round + 1}, 大小: {response_size} bytes")
                    except:
                        break  # 没有更多数据包时退出当前轮次
            except Exception as e:
                self.logger.debug(f"清理监听器队列异常 - 轮次: {cleanup_round + 1}, 错误: {e}")

            # 如果本轮没有清理到任何数据包，说明队列已空，可以提前结束
            if round_cleaned == 0:
                break

        self.logger.debug(f"监听器队列清理完成 - ID: {video_id}, 清理数据包数量: {cleaned_packets}")

        # 直接访问页面，无需重新设置监听器
        self.logger.debug(f"访问视频页面: {video_url}")
        self.dp.get(video_url)

        # ---- 首包数据获取 ----
        data = None
        last_error = ""

        for retry_count in range(self.JS_RETRY):
            try:
                pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                response_size = len(pkt.response.body) if pkt.response.body else 0
                self.logger.debug(f"获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                data = self._to_json(pkt.response.body)
                if (data or {}).get('aweme_detail'):
                    # 关键修复：验证获取到的视频ID是否与请求的ID一致
                    received_video_id = (data or {}).get('aweme_detail', {}).get('aweme_id', '')

                    # 严格数据验证（可配置）
                    if self.STRICT_DATA_VALIDATION:
                        if received_video_id == video_id:
                            self.logger.debug(f"严格验证通过 - 请求ID: {video_id}, 接收ID: {received_video_id}")
                            break
                        else:
                            self.logger.warning(f"严格验证失败 - 请求ID: {video_id}, 接收ID: {received_video_id}, 重试: {retry_count + 1}")
                            data = None  # 重置数据，继续重试
                            continue
                    else:
                        # 非严格模式，只要有aweme_detail就接受
                        self.logger.debug(f"非严格模式获取数据 - 请求ID: {video_id}, 接收ID: {received_video_id}")
                        break
                else:
                    self.logger.warning(f"响应中无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
            except Exception as e:
                # 记录最后一次错误，用于后续错误分类
                last_error = str(e)
                error_type = self._classify_error(last_error, data)
                self.logger.warning(f"获取响应包异常 - ID: {video_id}, 错误类型: {error_type}, 错误: {e}, 重试: {retry_count + 1}")

            if retry_count < self.JS_RETRY - 1:
                time.sleep(self.SLEEP_BETWEEN_TRIES)

        # 改进的错误判断逻辑：区分JSON解析错误和真正的滑块验证需求
        if not ((data or {}).get('aweme_detail')):
            if self._should_trigger_captcha(data, last_error):
                self.logger.warning(f"检测到需要滑块验证 - ID: {video_id}")
                self._wait_user_to_solve('获取视频信息（首包）')

                for retry_count in range(self.JS_RETRY):
                    self.dp.refresh()
                    try:
                        pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                        response_size = len(pkt.response.body) if pkt.response.body else 0
                        self.logger.debug(f"验证后获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                        data = self._to_json(pkt.response.body)
                        if (data or {}).get('aweme_detail'):
                            # 验证后也需要进行ID一致性检查
                            received_video_id = (data or {}).get('aweme_detail', {}).get('aweme_id', '')

                            if self.STRICT_DATA_VALIDATION:
                                if received_video_id == video_id:
                                    self.logger.info(f"验证后严格验证通过 - 请求ID: {video_id}, 接收ID: {received_video_id}")
                                    break
                                else:
                                    self.logger.warning(f"验证后严格验证失败 - 请求ID: {video_id}, 接收ID: {received_video_id}, 重试: {retry_count + 1}")
                                    data = None
                                    continue
                            else:
                                self.logger.info(f"验证后非严格模式获取数据 - 请求ID: {video_id}, 接收ID: {received_video_id}")
                                break
                        else:
                            self.logger.warning(f"验证后响应中仍无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
                    except Exception as e:
                        self.logger.warning(f"验证后获取响应包异常 - ID: {video_id}, 错误: {e}, 重试: {retry_count + 1}")

                    if retry_count < self.JS_RETRY - 1:
                        time.sleep(self.SLEEP_BETWEEN_TRIES)
            else:
                # 技术错误，不需要人工验证，返回基本信息
                error_type = self._classify_error(last_error, data)
                self.logger.info(f"检测到技术错误({error_type})，返回基本信息 - ID: {video_id}")
                return self._get_basic_video_info(video_id)

        video_detail = (data or {}).get('aweme_detail')
        if not video_detail:
            self.logger.error(f"多次尝试后仍未获取到视频详情数据 - ID: {video_id}")
            return self._get_basic_video_info(video_id)

        # 处理视频详情数据（与原方法相同的逻辑）
        return self._process_video_detail(video_detail, video_id)

    def _get_basic_video_info(self, video_id: str) -> Dict:
        """当无法获取详细信息时，返回基本的视频信息"""
        return {
            '视频id': video_id,
            '视频描述': '获取失败',
            '发布时间': '',
            '视频时长': 0,
            '作者UID': '未知',
            '作者昵称': '未知',
            '作者抖音号': '未知',
            '作者签名': '未知',
            '作者关注数': 0,
            '作者获赞数': 0,
            '作者secid': '未知',
            '视频点赞数': 0,
            '视频评论数': 0,
            '视频分享数': 0,
            '视频收藏数': 0,
            '视频播放数': 0,
            '视频链接': f"https://www.douyin.com/video/{video_id}"
        }

    def _process_video_detail(self, video_detail: dict, video_id: str) -> Dict:
        """处理视频详情数据，提取需要的字段"""
        video_url = f"https://www.douyin.com/video/{video_id}"

        # 定义需要保留的字段及其路径映射（与fetch_video_info保持一致）
        keep_fields = {
            '视频id': 'aweme_id',
            '视频描述': 'desc',
            '发布时间': 'create_time',  # 特殊处理：需要格式化
            '视频时长': 'duration',
            '作者UID': 'author.uid',
            '作者昵称': 'author.nickname',
            '作者抖音号': 'author.unique_id',
            '作者签名': 'author.signature',
            '作者关注数': 'author.following_count',
            '作者获赞数': 'author.favoriting_count',
            '作者secid': 'author.sec_uid',
            '视频点赞数': 'statistics.digg_count',
            '视频评论数': 'statistics.comment_count',
            '视频分享数': 'statistics.share_count',
            '视频收藏数': 'statistics.collect_count',
            '视频播放数': 'statistics.play_count',
            '视频链接': None  # 特殊处理：使用构建的URL
        }

        def get_nested_value(obj, path, default=''):
            """安全地获取嵌套字典的值"""
            if not path:
                return default
            try:
                parts = re.split(r'[\.\[\]]', path)
                parts = [p for p in parts if p]
                current = obj
                for part in parts:
                    if part.isdigit():
                        current = current[int(part)]
                    else:
                        current = current[part]
                return current if current is not None else default
            except (KeyError, TypeError, AttributeError, IndexError):
                return default

        # 处理单个视频对象，提取指定字段
        cleaned_video = {}
        for new_field, path in keep_fields.items():
            if new_field == '视频链接':
                # 特殊处理：使用构建的URL
                cleaned_video[new_field] = video_url
            elif new_field == '发布时间':
                # 特殊处理：将时间戳转换为格式化的日期时间字符串
                create_time = get_nested_value(video_detail, path)
                if create_time:
                    try:
                        formatted_time = datetime.datetime.fromtimestamp(int(create_time)).strftime('%Y-%m-%d %H:%M:%S')
                        cleaned_video[new_field] = formatted_time
                    except (ValueError, TypeError):
                        cleaned_video[new_field] = ''
                else:
                    cleaned_video[new_field] = ''
            else:
                cleaned_video[new_field] = get_nested_value(video_detail, path)

        return cleaned_video



    def fetch_favorites(self, sec_uid: str, max_items: int = 200) -> List[Dict]:
        """获取用户点赞的视频列表（演示），同样按“任何 JS 超时 → 滑块处理”。"""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/favorite/?device_platform=webapp")
        like_url = f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main&showTab=like"

        try:
            # 打开点赞页 → 首包
            self.dp.get(like_url)

            first_req, data = None, None
            for retry_count in range(self.JS_RETRY):
                try:
                    pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                    first_req = pkt.request.url
                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_list'):
                        break
                    else:
                        self.logger.warning(f"首包响应中无 aweme_list 字段, 重试: {retry_count + 1}")
                except Exception as e:
                    self.logger.warning(f"获取喜欢首包异常: {e}, 重试: {retry_count + 1}")
                    data = None

                if retry_count < self.JS_RETRY - 1:
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((data or {}).get('aweme_list')):
                self._wait_user_to_solve('获取喜欢列表（首包）')
                for retry_count in range(self.JS_RETRY):
                    self.dp.refresh()
                    try:
                        pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                        first_req = pkt.request.url
                        data = self._to_json(pkt.response.body)
                        if (data or {}).get('aweme_list'):
                            break
                        else:
                            self.logger.warning(f"验证后首包响应中仍无 aweme_list 字段, 重试: {retry_count + 1}")
                    except Exception as e:
                        self.logger.warning(f"验证后获取喜欢首包异常: {e}, 重试: {retry_count + 1}")
                        data = None

                    if retry_count < self.JS_RETRY - 1:
                        time.sleep(self.SLEEP_BETWEEN_TRIES)

            items = (data or {}).get('aweme_list') or []
            if not items:
                raise RuntimeError("多次尝试后仍未获取到喜欢首包数据，请稍后再试。")

            has_more = data.get('has_more', 0)
            cursor = data.get('max_cursor', 0)
            print(f"[首包-喜欢] items={len(items)} has_more={has_more} "
                  f"min_cursor={data.get('min_cursor')} max_cursor={data.get('max_cursor')}")

            # 翻页
            while has_more and len(items) < max_items:
                next_url = self._set_query_params(first_req, max_cursor=cursor, min_cursor=0)
                page_data = self._fetch_json_via_js(next_url, scene='获取喜欢列表（翻页）')

                page_items = (page_data or {}).get('aweme_list') or []
                items.extend(page_items)
                has_more = (page_data or {}).get('has_more', 0)
                cursor = (page_data or {}).get('max_cursor', 0)

                print(f"[喜欢翻页] items={len(page_items)} has_more={has_more} "
                      f"min_cursor={(page_data or {}).get('min_cursor')} "
                      f"max_cursor={(page_data or {}).get('max_cursor')} 累积={len(items)}")

                time.sleep(self.SLEEP_BETWEEN_PAGES)

            
            # 处理视频数据，获取每个视频的详细信息
            def get_nested_value(data, path, default=''):
                """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
                try:
                    current = data
                    # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
                    import re
                    parts = re.split(r'[\.\[\]]', path)
                    parts = [p for p in parts if p]  # 移除空字符串

                    for part in parts:
                        if part.isdigit():  # 数组索引
                            current = current[int(part)]
                        else:  # 字典键
                            current = current[part]
                    return current if current is not None else default
                except (KeyError, TypeError, AttributeError, IndexError):
                    return default

            # 获取每个视频的详细信息
            self.logger.info(f"开始获取 {len(items)} 个视频的详细信息")
            detailed_favorites = []
            success_count = 0
            fail_count = 0

            # 去重机制：跟踪已处理的视频ID
            processed_video_ids = set() if self.ENABLE_DEDUPLICATION else None
            duplicate_count = 0

            # 优化：为批量视频处理设置一次性监听器，避免重复设置
            self.logger.info("设置视频详情监听器（批量处理优化）")
            self.dp.listen.clear()
            self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")

            # 架构性能瓶颈：串行处理架构，无并发优化
            # 总处理时间 = (单视频时间 + 1.5s延迟) × 视频数量
            # 优化建议：使用DrissionPage 4.x的多标签页支持实现有限并发
            for idx, item in enumerate(items, 1):
                video_id = get_nested_value(item, 'aweme_id')
                if video_id:
                    # 去重检查
                    if self.ENABLE_DEDUPLICATION and processed_video_ids is not None:
                        if video_id in processed_video_ids:
                            duplicate_count += 1
                            self.logger.debug(f"[{idx}/{len(items)}] 跳过重复视频 - ID: {video_id}")
                            continue
                        processed_video_ids.add(video_id)

                    start_time = time.time()
                    try:
                        self.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                        # 性能优化：使用优化版本的视频信息获取，复用已设置的监听器
                        # 获取视频详细信息
                        video_detail = self._fetch_video_info_optimized(video_id)
                        detailed_favorites.append(video_detail)

                        elapsed_time = time.time() - start_time
                        success_count += 1

                        self.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")
                        print(f"已获取视频 {video_id} 的详细信息 (耗时: {elapsed_time:.2f}s)")

                        # 性能影响：每个视频后强制等待1.5秒
                        # 防风控设计：降低被检测为高频请求的概率
                        # 添加延迟避免请求过快
                        time.sleep(self.SLEEP_BETWEEN_PAGES)

                    except Exception as e:
                        elapsed_time = time.time() - start_time
                        fail_count += 1

                        self.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 错误: {e}, 耗时: {elapsed_time:.2f}s")
                        print(f"获取视频 {video_id} 详情失败: {e} (耗时: {elapsed_time:.2f}s)")

                        # 如果获取详情失败，使用基础信息
                        basic_info = {
                            '视频id': get_nested_value(item, 'aweme_id'),
                            '视频描述': get_nested_value(item, 'desc'),
                            '创建时间': get_nested_value(item, 'create_time'),
                            '视频作者昵称': get_nested_value(item, 'author.nickname'),
                        }
                        detailed_favorites.append(basic_info)
                        continue

            # 添加去重统计信息
            if self.ENABLE_DEDUPLICATION and duplicate_count > 0:
                self.logger.info(f"去重统计 - 发现并跳过 {duplicate_count} 个重复视频")

            self.logger.info(f"视频详情获取完成 - 成功: {success_count}, 失败: {fail_count}, 重复: {duplicate_count}, 总计: {len(items)}")

            return detailed_favorites[:max_items]

        finally:
            self.dp.listen.pause(True)



    def fetch_favorites_stage1_optimized(self, sec_uid: str, max_items: int = 200) -> List[Dict]:
        """阶段一：监听器复用优化 - 预期提升60-70%"""

        # 获取视频列表（保持现有逻辑）
        items = self._get_video_list(sec_uid, max_items)

        # ⭐ 关键优化：为整个批处理设置一次监听器
        self.logger.info("设置视频详情监听器（批量处理优化）")
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")

        detailed_favorites = []
        success_count = 0

        try:
            for idx, item in enumerate(items, 1):
                video_id = item.get('aweme_id')
                if not video_id:
                    continue

                start_time = time.time()
                try:
                    self.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                    # ⭐ 使用复用监听器的优化版本
                    video_detail = self._fetch_video_info_reuse_listener(video_id)
                    detailed_favorites.append(video_detail)

                    elapsed_time = time.time() - start_time
                    success_count += 1

                    self.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")

                    # 保持防风控延迟
                    time.sleep(self.SLEEP_BETWEEN_PAGES)

                except Exception as e:
                    elapsed_time = time.time() - start_time
                    self.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 耗时: {elapsed_time:.2f}s, 错误: {e}")

            self.logger.info(f"批量处理完成 - 成功: {success_count}/{len(items)}")
            return detailed_favorites

        finally:
            self.dp.listen.stop()  # 使用推荐的stop()方法

    def _get_video_list(self, sec_uid: str, max_items: int) -> List[Dict]:
        """获取视频列表的辅助方法，从现有的fetch_favorites方法中提取"""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/favorite/?device_platform=webapp")
        like_url = f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main&showTab=like"

        try:
            # 打开点赞页 → 首包
            self.dp.get(like_url)

            first_req, data = None, None
            for retry_count in range(self.JS_RETRY):
                try:
                    pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                    first_req = pkt.request.url
                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_list'):
                        break
                    else:
                        self.logger.warning(f"首包响应中无 aweme_list 字段, 重试: {retry_count + 1}")
                except Exception as e:
                    self.logger.warning(f"获取喜欢首包异常: {e}, 重试: {retry_count + 1}")
                    data = None

                if retry_count < self.JS_RETRY - 1:
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((data or {}).get('aweme_list')):
                self._wait_user_to_solve('获取喜欢列表（首包）')
                for retry_count in range(self.JS_RETRY):
                    self.dp.refresh()
                    try:
                        pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                        first_req = pkt.request.url
                        data = self._to_json(pkt.response.body)
                        if (data or {}).get('aweme_list'):
                            break
                        else:
                            self.logger.warning(f"验证后首包响应中仍无 aweme_list 字段, 重试: {retry_count + 1}")
                    except Exception as e:
                        self.logger.warning(f"验证后获取喜欢首包异常: {e}, 重试: {retry_count + 1}")
                        data = None

                    if retry_count < self.JS_RETRY - 1:
                        time.sleep(self.SLEEP_BETWEEN_TRIES)

            items = (data or {}).get('aweme_list') or []
            if not items:
                raise RuntimeError("多次尝试后仍未获取到喜欢首包数据，请稍后再试。")

            has_more = data.get('has_more', 0)
            cursor = data.get('max_cursor', 0)
            print(f"[首包-喜欢] items={len(items)} has_more={has_more} "
                  f"min_cursor={data.get('min_cursor')} max_cursor={data.get('max_cursor')}")

            # 翻页
            while has_more and len(items) < max_items:
                next_url = self._set_query_params(first_req, max_cursor=cursor, min_cursor=0)
                page_data = self._fetch_json_via_js(next_url, scene='获取喜欢列表（翻页）')

                page_items = (page_data or {}).get('aweme_list') or []
                items.extend(page_items)
                has_more = (page_data or {}).get('has_more', 0)
                cursor = (page_data or {}).get('max_cursor', 0)

                print(f"[喜欢翻页] items={len(page_items)} has_more={has_more} "
                      f"min_cursor={(page_data or {}).get('min_cursor')} "
                      f"max_cursor={(page_data or {}).get('max_cursor')} 累积={len(items)}")

                time.sleep(self.SLEEP_BETWEEN_PAGES)

            return items[:max_items]

        finally:
            self.dp.listen.pause(True)

    def _fetch_video_info_reuse_listener(self, video_id: str) -> Dict:
        """复用监听器的优化版本 - 节省1-2秒/视频"""

        video_url = f"https://www.douyin.com/video/{video_id}"

        # ⭐ 关键优化：直接访问页面，无需重新设置监听器
        self.dp.get(video_url)

        # 使用推荐的 listen.wait() API
        try:
            packet = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
            data = self._to_json(packet.response.body)

            if data and data.get('aweme_detail'):
                return self._process_video_detail(data['aweme_detail'], video_id)
            else:
                self.logger.warning(f"响应中无 aweme_detail 字段 - ID: {video_id}")
                return self._get_basic_video_info(video_id)

        except Exception as e:
            self.logger.error(f"获取视频详情异常 - ID: {video_id}, 错误: {e}")
            return self._get_basic_video_info(video_id)

    def _fetch_video_info_with_queue_cleanup(self, video_id: str) -> Dict:
        """阶段二：添加队列清理机制，确保数据准确性"""

        # ⭐ 队列清理：防止数据包混乱
        cleaned_packets = self._clean_listener_queue_optimized()
        self.logger.debug(f"清理监听器队列 - ID: {video_id}, 清理数据包数量: {cleaned_packets}")

        video_url = f"https://www.douyin.com/video/{video_id}"
        self.dp.get(video_url)

        try:
            packet = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
            data = self._to_json(packet.response.body)

            if data and data.get('aweme_detail'):
                # ⭐ 严格数据验证：确保获取到的是正确的视频ID
                received_video_id = data.get('aweme_detail', {}).get('aweme_id', '')
                if received_video_id != video_id:
                    self.logger.warning(f"视频ID不匹配 - 请求: {video_id}, 接收: {received_video_id}")
                    return self._get_basic_video_info(video_id)

                return self._process_video_detail(data['aweme_detail'], video_id)
            else:
                return self._get_basic_video_info(video_id)

        except Exception as e:
            self.logger.error(f"获取视频详情异常 - ID: {video_id}, 错误: {e}")
            return self._get_basic_video_info(video_id)

    def _clean_listener_queue_optimized(self) -> int:
        """优化的监听器队列清理方法"""
        cleaned_count = 0
        timeout = self.LISTENER_CLEANUP_TIMEOUT / self.LISTENER_CLEANUP_ROUNDS  # 分配超时时间

        try:
            for _ in range(self.LISTENER_CLEANUP_ROUNDS):
                round_cleaned = 0
                start_time = time.time()

                while time.time() - start_time < timeout:
                    try:
                        # 尝试获取队列中的旧数据包
                        old_packet = self.dp.listen.wait(timeout=0.1, raise_err=False)
                        if old_packet is None:
                            break
                        round_cleaned += 1
                        cleaned_count += 1

                        # 防止无限循环
                        if round_cleaned > 5:  # 每轮最多清理5个包
                            break
                    except:
                        break

                if round_cleaned == 0:
                    break  # 如果这轮没有清理到任何包，提前结束

        except Exception as e:
            self.logger.debug(f"队列清理过程中出现异常: {e}")

        return cleaned_count

    def fetch_favorites_stage2_optimized(self, sec_uid: str, max_items: int = 200) -> List[Dict]:
        """阶段二：监听器复用 + 队列清理机制优化"""

        # 获取视频列表（保持现有逻辑）
        items = self._get_video_list(sec_uid, max_items)

        # ⭐ 关键优化：为整个批处理设置一次监听器
        self.logger.info("设置视频详情监听器（批量处理优化 + 队列清理）")
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")

        detailed_favorites = []
        success_count = 0

        try:
            for idx, item in enumerate(items, 1):
                video_id = item.get('aweme_id')
                if not video_id:
                    continue

                start_time = time.time()
                try:
                    self.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                    # ⭐ 使用带队列清理的优化版本
                    video_detail = self._fetch_video_info_with_queue_cleanup(video_id)
                    detailed_favorites.append(video_detail)

                    elapsed_time = time.time() - start_time
                    success_count += 1

                    self.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")

                    # 保持防风控延迟
                    time.sleep(self.SLEEP_BETWEEN_PAGES)

                except Exception as e:
                    elapsed_time = time.time() - start_time
                    self.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 耗时: {elapsed_time:.2f}s, 错误: {e}")

            self.logger.info(f"批量处理完成（阶段二优化） - 成功: {success_count}/{len(items)}")
            return detailed_favorites

        finally:
            self.dp.listen.stop()  # 使用推荐的stop()方法

    def fetch_favorites_stage3_optimized(self, sec_uid: str, max_items: int = 200) -> List[Dict]:
        """阶段三：监听器复用 + 队列清理 + 加载模式优化"""

        # 验证加载模式设置
        self.logger.info("验证加载模式优化设置")
        self._verify_load_mode_optimization()

        # 获取视频列表（保持现有逻辑）
        items = self._get_video_list(sec_uid, max_items)

        # ⭐ 关键优化：为整个批处理设置一次监听器
        self.logger.info("设置视频详情监听器（完整优化：监听器复用 + 队列清理 + 加载模式）")
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")

        detailed_favorites = []
        success_count = 0
        performance_stats = {
            'total_time': 0,
            'avg_time_per_video': 0,
            'queue_cleanups': 0,
            'data_validation_failures': 0
        }

        batch_start_time = time.time()

        try:
            for idx, item in enumerate(items, 1):
                video_id = item.get('aweme_id')
                if not video_id:
                    continue

                start_time = time.time()
                try:
                    self.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                    # ⭐ 使用完整优化版本
                    video_detail = self._fetch_video_info_with_queue_cleanup(video_id)
                    detailed_favorites.append(video_detail)

                    elapsed_time = time.time() - start_time
                    success_count += 1
                    performance_stats['total_time'] += elapsed_time

                    self.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")

                    # 保持防风控延迟
                    time.sleep(self.SLEEP_BETWEEN_PAGES)

                except Exception as e:
                    elapsed_time = time.time() - start_time
                    self.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 耗时: {elapsed_time:.2f}s, 错误: {e}")

            # 计算性能统计
            total_batch_time = time.time() - batch_start_time
            if success_count > 0:
                performance_stats['avg_time_per_video'] = performance_stats['total_time'] / success_count

            self.logger.info(f"批量处理完成（阶段三完整优化） - 成功: {success_count}/{len(items)}")
            self.logger.info(f"性能统计 - 总耗时: {total_batch_time:.2f}s, 平均每视频: {performance_stats['avg_time_per_video']:.2f}s")

            return detailed_favorites

        finally:
            self.dp.listen.stop()  # 使用推荐的stop()方法

    def _verify_load_mode_optimization(self):
        """验证加载模式优化设置"""
        try:
            # 检查当前加载模式
            # 注意：DrissionPage 4.x 可能没有直接的方法来查询当前加载模式
            # 这里主要是记录优化已启用的信息
            self.logger.info("加载模式优化已启用：")
            self.logger.info("  - 图片加载已禁用 (no_imgs)")
            self.logger.info("  - 加载模式设置为 'none' (发送请求后立即返回)")
            self.logger.info("  - 音频已静音 (mute)")
            self.logger.info("  - GPU加速已禁用")
            self.logger.info("  - 扩展和插件已禁用")
        except Exception as e:
            self.logger.warning(f"验证加载模式时出现异常: {e}")

    def fetch_favorites_optimized(self, sec_uid: str, max_items: int = 200) -> List[Dict]:
        """
        统一的优化方法，根据配置文件自动选择优化阶段

        优化阶段说明：
        - 阶段1：监听器复用优化（预期提升60-70%）
        - 阶段2：监听器复用 + 队列清理机制
        - 阶段3：监听器复用 + 队列清理 + 加载模式优化（完整优化）
        """

        self.logger.info(f"启用优化阶段 {self.OPTIMIZATION_STAGE}")

        if self.OPTIMIZATION_STAGE == 1:
            self.logger.info("使用阶段一优化：监听器复用")
            return self.fetch_favorites_stage1_optimized(sec_uid, max_items)
        elif self.OPTIMIZATION_STAGE == 2:
            self.logger.info("使用阶段二优化：监听器复用 + 队列清理")
            return self.fetch_favorites_stage2_optimized(sec_uid, max_items)
        elif self.OPTIMIZATION_STAGE == 3:
            self.logger.info("使用阶段三优化：完整优化（监听器复用 + 队列清理 + 加载模式）")
            return self.fetch_favorites_stage3_optimized(sec_uid, max_items)
        else:
            self.logger.warning(f"无效的优化阶段 {self.OPTIMIZATION_STAGE}，使用默认的阶段三优化")
            return self.fetch_favorites_stage3_optimized(sec_uid, max_items)

    # ===============================================
    # 并发处理相关方法 - DrissionPage 4.x 多标签页并发
    # ===============================================

    class ConcurrentVideoFetcher:
        """多标签页并发视频信息获取器"""
        
        def __init__(self, scraper_instance, max_tabs: int):
            self.scraper = scraper_instance
            self.max_tabs = max_tabs
            self.tabs = []  # 标签页池
            self.tab_lock = threading.Lock()
            self.available_tabs = queue.Queue()  # 可用标签页队列
            self.logger = scraper_instance.logger
            self.results = []  # 线程安全的结果列表
            self.results_lock = threading.Lock()
            self.processed_ids = set()  # 去重集合
            self.error_count = 0
            self.success_count = 0
            
        def initialize_tabs(self):
            """初始化标签页池"""
            self.logger.info(f"开始初始化 {self.max_tabs} 个标签页")
            
            # 获取浏览器实例
            browser = self.scraper.dp.browser
            
            for i in range(self.max_tabs):
                try:
                    # 创建新标签页，避免同时创建触发风控
                    if i > 0:
                        time.sleep(self.scraper.TAB_INIT_DELAY)
                    
                    # 使用浏览器实例创建新标签页
                    tab_id = browser.new_tab()
                    tab = browser.get_tab(tab_id)
                    
                    # 为每个标签页设置监听器
                    tab.listen.clear()
                    tab.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")
                    
                    self.tabs.append(tab)
                    self.available_tabs.put(tab)
                    
                    if self.scraper.CONCURRENT_DEBUG_LOGGING:
                        self.logger.debug(f"标签页 {i+1} 初始化完成，ID: {tab_id}")
                    
                except Exception as e:
                    self.logger.error(f"初始化标签页 {i+1} 失败: {e}")
                    
            self.logger.info(f"标签页池初始化完成，可用标签页数量: {len(self.tabs)}")
            
        def get_tab(self) -> object:
            """获取可用标签页"""
            return self.available_tabs.get()
            
        def return_tab(self, tab):
            """归还标签页到池中"""
            self.available_tabs.put(tab)
            
        def cleanup(self):
            """清理标签页池"""
            self.logger.info("开始清理标签页池")
            for tab in self.tabs:
                try:
                    tab.listen.stop()
                    # 注意：不要关闭标签页，让主程序管理
                except Exception as e:
                    self.logger.warning(f"清理标签页时出现异常: {e}")
                    
        def add_result(self, result: Dict, video_id: str):
            """线程安全地添加结果"""
            with self.results_lock:
                if video_id not in self.processed_ids:
                    self.results.append(result)
                    self.processed_ids.add(video_id)
                    self.success_count += 1
                    
        def add_error(self):
            """记录错误数量"""
            with self.results_lock:
                self.error_count += 1

    def _fetch_video_info_in_tab(self, tab, video_id: str) -> Dict:
        """在指定标签页中获取视频信息 - 并发专用方法"""
        if self.CONCURRENT_DEBUG_LOGGING:
            self.logger.debug(f"在标签页中开始获取视频详情 - ID: {video_id}")
        
        video_url = f"https://www.douyin.com/video/{video_id}"
        
        try:
            # 清理监听器队列中的旧数据包
            if self.CONCURRENT_DEBUG_LOGGING:
                self.logger.debug(f"清理标签页监听器队列 - ID: {video_id}")
            
            cleaned_packets = 0
            for cleanup_round in range(self.LISTENER_CLEANUP_ROUNDS):
                round_cleaned = 0
                try:
                    while True:
                        try:
                            old_pkt = tab.listen.wait(timeout=self.LISTENER_CLEANUP_TIMEOUT, raise_err=True)
                            round_cleaned += 1
                            cleaned_packets += 1
                            if round_cleaned > 5:  # 防止无限循环
                                break
                        except:
                            break
                except Exception:
                    break
                    
                if round_cleaned == 0:
                    break
                    
            if self.CONCURRENT_DEBUG_LOGGING and cleaned_packets > 0:
                self.logger.debug(f"清理了 {cleaned_packets} 个旧数据包 - ID: {video_id}")
            
            # 访问视频页面
            tab.get(video_url)
            
            # 获取响应数据
            data = None
            last_error = ""
            
            for retry_count in range(self.JS_RETRY):
                try:
                    pkt = tab.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                    data = self._to_json(pkt.response.body)
                    
                    if data and data.get('aweme_detail'):
                        # 验证视频ID一致性
                        if self.STRICT_DATA_VALIDATION:
                            received_video_id = data.get('aweme_detail', {}).get('aweme_id', '')
                            if received_video_id == video_id:
                                if self.CONCURRENT_DEBUG_LOGGING:
                                    self.logger.debug(f"并发模式验证通过 - ID: {video_id}")
                                break
                            else:
                                if self.CONCURRENT_DEBUG_LOGGING:
                                    self.logger.warning(f"并发模式ID不匹配 - 请求: {video_id}, 接收: {received_video_id}")
                                data = None
                                continue
                        else:
                            break
                    else:
                        if self.CONCURRENT_DEBUG_LOGGING:
                            self.logger.warning(f"响应中无aweme_detail - ID: {video_id}, 重试: {retry_count + 1}")
                        
                except Exception as e:
                    last_error = str(e)
                    if self.CONCURRENT_DEBUG_LOGGING:
                        self.logger.warning(f"获取响应异常 - ID: {video_id}, 错误: {e}, 重试: {retry_count + 1}")
                    
                if retry_count < self.JS_RETRY - 1:
                    time.sleep(self.SLEEP_BETWEEN_TRIES)
            
            # 处理获取到的数据
            video_detail = (data or {}).get('aweme_detail')
            if video_detail:
                return self._process_video_detail(video_detail, video_id)
            else:
                # 对于并发模式，技术错误时返回基本信息而不是触发滑块验证
                error_type = self._classify_error(last_error, data)
                if self.CONCURRENT_DEBUG_LOGGING:
                    self.logger.info(f"并发模式检测到技术错误({error_type})，返回基本信息 - ID: {video_id}")
                return self._get_basic_video_info(video_id)
                
        except Exception as e:
            self.logger.error(f"并发获取视频详情异常 - ID: {video_id}, 错误: {e}")
            return self._get_basic_video_info(video_id)

    def _concurrent_worker(self, fetcher: 'ConcurrentVideoFetcher', video_ids: List[str]):
        """并发工作线程函数"""
        thread_name = threading.current_thread().name
        
        for video_id in video_ids:
            try:
                # 获取标签页
                tab = fetcher.get_tab()
                
                if self.CONCURRENT_DEBUG_LOGGING:
                    self.logger.debug(f"[{thread_name}] 开始处理视频 - ID: {video_id}")
                
                start_time = time.time()
                
                # 获取视频信息
                video_info = self._fetch_video_info_in_tab(tab, video_id)
                
                # 保存结果
                fetcher.add_result(video_info, video_id)
                
                elapsed_time = time.time() - start_time
                self.logger.info(f"[{thread_name}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")
                
                # 归还标签页
                fetcher.return_tab(tab)
                
                # 防风控延迟
                time.sleep(self.SLEEP_BETWEEN_PAGES)
                
            except Exception as e:
                fetcher.add_error()
                self.logger.error(f"[{thread_name}] 处理视频失败 - ID: {video_id}, 错误: {e}")
                
                # 确保标签页被归还
                try:
                    fetcher.return_tab(tab)
                except:
                    pass

    def fetch_favorites_concurrent(self, sec_uid: str, max_items: int = 200) -> List[Dict]:
        """
        并发版本的喜欢视频获取方法 - 使用多标签页并发处理
        
        Args:
            sec_uid (str): 用户的sec_uid
            max_items (int): 最大获取数量
            
        Returns:
            List[Dict]: 视频详情列表
        """
        
        if not self.ENABLE_CONCURRENT_MODE:
            self.logger.warning("并发模式未启用，回退到串行模式")
            return self.fetch_favorites_optimized(sec_uid, max_items)
            
        self.logger.info(f"启用并发模式获取喜欢列表 - 并发数: {self.MAX_CONCURRENT_TABS}")
        
        # 获取视频列表（复用现有方法）
        video_items = self._get_video_list(sec_uid, max_items)
        
        if not video_items:
            self.logger.warning("未获取到视频列表")
            return []
        
        # 提取视频ID列表
        video_ids = []
        for item in video_items:
            video_id = item.get('aweme_id')
            if video_id:
                video_ids.append(video_id)
        
        if not video_ids:
            self.logger.warning("未提取到有效的视频ID")
            return []
            
        self.logger.info(f"准备并发处理 {len(video_ids)} 个视频")
        
        # 创建并发获取器
        fetcher = self.ConcurrentVideoFetcher(self, self.MAX_CONCURRENT_TABS)
        
        try:
            # 初始化标签页池
            fetcher.initialize_tabs()
            
            if len(fetcher.tabs) == 0:
                self.logger.error("标签页池初始化失败，回退到串行模式")
                return self.fetch_favorites_optimized(sec_uid, max_items)
            
            # 分批处理视频ID
            batch_size = max(1, len(video_ids) // len(fetcher.tabs))
            video_batches = [video_ids[i:i + batch_size] for i in range(0, len(video_ids), batch_size)]
            
            self.logger.info(f"将 {len(video_ids)} 个视频分为 {len(video_batches)} 批，每批约 {batch_size} 个")
            
            # 使用线程池执行并发任务
            with ThreadPoolExecutor(max_workers=len(fetcher.tabs), thread_name_prefix="ConcurrentVideo") as executor:
                futures = []
                
                for i, batch in enumerate(video_batches):
                    if batch:  # 确保批次不为空
                        future = executor.submit(self._concurrent_worker, fetcher, batch)
                        futures.append(future)
                
                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        self.logger.error(f"并发任务执行异常: {e}")
            
            # 收集结果
            self.logger.info(f"并发处理完成 - 成功: {fetcher.success_count}, 失败: {fetcher.error_count}, 总计: {len(video_ids)}")
            
            return fetcher.results[:max_items]
            
        except Exception as e:
            self.logger.error(f"并发处理异常: {e}")
            return []
            
        finally:
            # 清理标签页池
            fetcher.cleanup()

    def close(self):
        """退出并关闭浏览器."""
        self.dp.browser.quit()