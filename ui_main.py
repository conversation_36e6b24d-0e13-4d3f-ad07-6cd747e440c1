#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据抓取器 - 主UI界面
基于Qt6的现代化图形界面
"""

import sys
import os
import json
from datetime import datetime
from typing import Dict, List, Optional

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QTabWidget, QGroupBox, QLabel, QLineEdit, QSpinBox,
    QPushButton, QCheckBox, QComboBox, QProgressBar, QTextEdit,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QGridLayout, QScrollArea, QMessageBox, QFileDialog
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize
)
from PyQt6.QtGui import (
    QFont, QIcon, QPalette, QColor, QPixmap
)

# 导入后端逻辑
from douyin import DouyinScraper
from utils import load_config, json_to_csv, get_data_file_path
from logger import setup_logger


class ModernButton(QPushButton):
    """现代化按钮样式"""
    def __init__(self, text: str, button_type: str = "primary"):
        super().__init__(text)
        self.button_type = button_type
        self.setMinimumHeight(35)
        self.apply_style()
    
    def apply_style(self):
        if self.button_type == "primary":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #2563eb;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #1d4ed8;
                }
                QPushButton:pressed {
                    background-color: #1e40af;
                }
                QPushButton:disabled {
                    background-color: #9ca3af;
                }
            """)
        elif self.button_type == "success":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #059669;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #047857;
                }
                QPushButton:pressed {
                    background-color: #065f46;
                }
            """)
        elif self.button_type == "danger":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #dc2626;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #b91c1c;
                }
                QPushButton:pressed {
                    background-color: #991b1b;
                }
            """)


class ConfigPanel(QWidget):
    """配置面板"""
    def __init__(self):
        super().__init__()
        self.config = load_config()
        self.init_ui()
        self.load_config_values()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("配置设置")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 基础配置组
        self.create_basic_config_group(layout)
        
        # 抓取限制组
        self.create_limits_group(layout)
        
        # 性能优化组
        self.create_optimization_group(layout)
        
        # 导出设置组
        self.create_export_group(layout)
        
        # 添加弹性空间
        layout.addStretch()
        
        # 保存配置按钮
        save_btn = ModernButton("保存配置", "success")
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)
    
    def create_basic_config_group(self, parent_layout):
        """创建基础配置组"""
        group = QGroupBox("基础配置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #374151;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # 抖音ID输入
        layout.addWidget(QLabel("目标抖音ID:"), 0, 0)
        self.douyin_id_input = QLineEdit()
        self.douyin_id_input.setPlaceholderText("请输入要抓取的抖音用户ID")
        self.douyin_id_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #d1d5db;
                border-radius: 4px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #2563eb;
            }
        """)
        layout.addWidget(self.douyin_id_input, 0, 1)
        
        # 超时设置
        layout.addWidget(QLabel("JS执行超时(秒):"), 1, 0)
        self.js_timeout_input = QSpinBox()
        self.js_timeout_input.setRange(3, 60)
        self.js_timeout_input.setValue(10)
        layout.addWidget(self.js_timeout_input, 1, 1)
        
        # 重试次数
        layout.addWidget(QLabel("重试次数:"), 2, 0)
        self.js_retry_input = QSpinBox()
        self.js_retry_input.setRange(1, 10)
        self.js_retry_input.setValue(3)
        layout.addWidget(self.js_retry_input, 2, 1)
        
        parent_layout.addWidget(group)
    
    def create_limits_group(self, parent_layout):
        """创建抓取限制组"""
        group = QGroupBox("抓取限制")
        group.setStyleSheet(self.get_group_style())
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # 最大粉丝数量
        layout.addWidget(QLabel("最大粉丝数量:"), 0, 0)
        self.max_follower_input = QSpinBox()
        self.max_follower_input.setRange(1, 10000)
        self.max_follower_input.setValue(30)
        layout.addWidget(self.max_follower_input, 0, 1)
        
        # 最大喜欢数量
        layout.addWidget(QLabel("最大喜欢数量:"), 1, 0)
        self.max_favorite_input = QSpinBox()
        self.max_favorite_input.setRange(1, 1000)
        self.max_favorite_input.setValue(20)
        layout.addWidget(self.max_favorite_input, 1, 1)
        
        parent_layout.addWidget(group)
    
    def create_optimization_group(self, parent_layout):
        """创建性能优化组"""
        group = QGroupBox("性能优化")
        group.setStyleSheet(self.get_group_style())
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # 优化阶段选择
        layout.addWidget(QLabel("优化阶段:"), 0, 0)
        self.optimization_stage = QComboBox()
        self.optimization_stage.addItems([
            "1 - 基础优化",
            "2 - 监听器复用",
            "3 - 完整优化"
        ])
        self.optimization_stage.setCurrentIndex(2)
        layout.addWidget(self.optimization_stage, 0, 1)
        
        # 并发模式
        self.enable_concurrent = QCheckBox("启用并发模式")
        self.enable_concurrent.setChecked(True)
        layout.addWidget(self.enable_concurrent, 1, 0, 1, 2)
        
        # 并发标签页数
        layout.addWidget(QLabel("并发标签页数:"), 2, 0)
        self.max_concurrent_tabs = QSpinBox()
        self.max_concurrent_tabs.setRange(1, 10)
        self.max_concurrent_tabs.setValue(5)
        layout.addWidget(self.max_concurrent_tabs, 2, 1)
        
        parent_layout.addWidget(group)
    
    def create_export_group(self, parent_layout):
        """创建导出设置组"""
        group = QGroupBox("导出设置")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        
        # 导出格式选择
        self.export_follower_json = QCheckBox("导出粉丝JSON")
        self.export_follower_csv = QCheckBox("导出粉丝CSV")
        self.export_favorite_json = QCheckBox("导出喜欢JSON")
        self.export_favorite_csv = QCheckBox("导出喜欢CSV")
        
        # 默认全选
        for checkbox in [self.export_follower_json, self.export_follower_csv,
                        self.export_favorite_json, self.export_favorite_csv]:
            checkbox.setChecked(True)
            layout.addWidget(checkbox)
        
        parent_layout.addWidget(group)
    
    def get_group_style(self):
        """获取组框样式"""
        return """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #374151;
            }
        """
    
    def load_config_values(self):
        """从配置文件加载值"""
        # 基础配置
        douyin_config = self.config.get('douyin_id', {})
        self.douyin_id_input.setText(douyin_config.get('douyin_id', ''))
        
        scraper_config = self.config.get('scraper', {})
        self.js_timeout_input.setValue(scraper_config.get('js_timeout', 10))
        self.js_retry_input.setValue(scraper_config.get('js_retry', 3))
        
        # 限制配置
        limits_config = self.config.get('limits', {})
        self.max_follower_input.setValue(limits_config.get('max_follower_count', 30))
        self.max_favorite_input.setValue(limits_config.get('max_favorite_count', 20))
        
        # 优化配置
        opt_config = self.config.get('optimization', {})
        stage = opt_config.get('optimization_stage', 3)
        self.optimization_stage.setCurrentIndex(stage - 1)
        
        concurrent_config = self.config.get('concurrent', {})
        self.enable_concurrent.setChecked(concurrent_config.get('enable_concurrent_mode', True))
        self.max_concurrent_tabs.setValue(concurrent_config.get('max_concurrent_tabs', 5))
        
        # 导出配置
        export_config = self.config.get('export', {})
        self.export_follower_json.setChecked(export_config.get('follower_json', True))
        self.export_follower_csv.setChecked(export_config.get('follower_csv', True))
        self.export_favorite_json.setChecked(export_config.get('favorite_json', True))
        self.export_favorite_csv.setChecked(export_config.get('favorite_csv', True))
    
    def get_config_dict(self) -> Dict:
        """获取当前配置字典"""
        return {
            'douyin_id': {
                'douyin_id': self.douyin_id_input.text().strip()
            },
            'scraper': {
                'js_timeout': self.js_timeout_input.value(),
                'js_retry': self.js_retry_input.value(),
                'sleep_between_tries': 0.8,
                'sleep_between_pages': 1.5
            },
            'limits': {
                'max_follower_count': self.max_follower_input.value(),
                'max_favorite_count': self.max_favorite_input.value()
            },
            'optimization': {
                'optimization_stage': self.optimization_stage.currentIndex() + 1,
                'enable_performance_stats': True,
                'enable_debug_logging': False
            },
            'concurrent': {
                'enable_concurrent_mode': self.enable_concurrent.isChecked(),
                'max_concurrent_tabs': self.max_concurrent_tabs.value()
            },
            'export': {
                'follower_json': self.export_follower_json.isChecked(),
                'follower_csv': self.export_follower_csv.isChecked(),
                'favorite_json': self.export_favorite_json.isChecked(),
                'favorite_csv': self.export_favorite_csv.isChecked()
            }
        }
    
    def save_config(self):
        """保存配置到文件"""
        try:
            config_dict = self.get_config_dict()
            
            # 写入config.toml文件的简化版本
            # 这里简化处理，实际应该使用toml库
            QMessageBox.information(self, "保存成功", "配置已保存！")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"配置保存失败：{str(e)}")


class ScraperWorker(QThread):
    """数据抓取工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新
    status_updated = pyqtSignal(str)    # 状态更新  
    log_message = pyqtSignal(str)       # 日志消息
    data_received = pyqtSignal(dict)    # 数据接收
    error_occurred = pyqtSignal(str)    # 错误发生
    finished = pyqtSignal()             # 完成信号
    
    def __init__(self, config: Dict, scrape_types: List[str]):
        super().__init__()
        self.config = config
        self.scrape_types = scrape_types
        self.is_running = True
        self.scraper = None
    
    def run(self):
        """执行抓取任务"""
        try:
            self.status_updated.emit("初始化抓取器...")
            self.scraper = DouyinScraper()
            
            douyin_id = self.config['douyin_id']['douyin_id']
            if not douyin_id:
                self.error_occurred.emit("请输入抖音ID")
                return
            
            # 获取sec_uid
            self.status_updated.emit("获取用户sec_uid...")
            self.progress_updated.emit(10)
            sec_uid = self.scraper.fetch_sec_uid(douyin_id)
            
            if not sec_uid:
                self.error_occurred.emit("无法获取用户sec_uid，请检查抖音ID")
                return
            
            self.log_message.emit(f"成功获取sec_uid: {sec_uid}")
            
            # 根据选择的类型执行抓取
            total_tasks = len(self.scrape_types)
            current_task = 0
            
            results = {}
            
            for scrape_type in self.scrape_types:
                if not self.is_running:
                    break
                
                current_task += 1
                base_progress = int((current_task - 1) / total_tasks * 80) + 10
                
                if scrape_type == "user_profile":
                    self.status_updated.emit("抓取用户信息...")
                    self.progress_updated.emit(base_progress)
                    user_data = self.scraper.fetch_user_profile(sec_uid)
                    results['user_profile'] = user_data
                    self.data_received.emit({'type': 'user_profile', 'data': user_data})
                
                elif scrape_type == "followers":
                    max_count = self.config['limits']['max_follower_count']
                    self.status_updated.emit(f"抓取粉丝列表 (最多{max_count}条)...")
                    self.progress_updated.emit(base_progress)
                    
                    followers = self.scraper.fetch_followers(sec_uid, max_count)
                    results['followers'] = followers
                    self.data_received.emit({'type': 'followers', 'data': followers})
                
                elif scrape_type == "favorites":
                    max_count = self.config['limits']['max_favorite_count']
                    self.status_updated.emit(f"抓取喜欢列表 (最多{max_count}条)...")
                    self.progress_updated.emit(base_progress)
                    
                    favorites = self.scraper.fetch_favorites(sec_uid, max_count)
                    results['favorites'] = favorites
                    self.data_received.emit({'type': 'favorites', 'data': favorites})
            
            self.progress_updated.emit(100)
            self.status_updated.emit("抓取完成")
            self.log_message.emit("所有抓取任务完成！")
            
        except Exception as e:
            self.error_occurred.emit(f"抓取失败: {str(e)}")
        finally:
            self.finished.emit()
    
    def stop(self):
        """停止抓取"""
        self.is_running = False
        self.quit()
        self.wait()


class ControlPanel(QWidget):
    """功能控制面板"""
    
    # 信号定义
    start_scraping = pyqtSignal(list)  # 开始抓取信号
    stop_scraping = pyqtSignal()       # 停止抓取信号
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("抓取控制")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 抓取类型选择组
        self.create_scrape_type_group(layout)
        
        # 控制按钮组
        self.create_control_buttons(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def create_scrape_type_group(self, parent_layout):
        """创建抓取类型选择组"""
        group = QGroupBox("抓取类型")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #374151;
            }
        """)
        
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        
        # 抓取类型复选框
        self.scrape_user_profile = QCheckBox("用户基本信息")
        self.scrape_followers = QCheckBox("粉丝列表")
        self.scrape_favorites = QCheckBox("喜欢列表")
        
        # 默认选中
        self.scrape_user_profile.setChecked(True)
        self.scrape_followers.setChecked(True)
        self.scrape_favorites.setChecked(True)
        
        for checkbox in [self.scrape_user_profile, self.scrape_followers, self.scrape_favorites]:
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 14px;
                    spacing: 8px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #d1d5db;
                    border-radius: 4px;
                    background-color: white;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #2563eb;
                    border-radius: 4px;
                    background-color: #2563eb;
                }
            """)
            layout.addWidget(checkbox)
        
        parent_layout.addWidget(group)
    
    def create_control_buttons(self, parent_layout):
        """创建控制按钮组"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 开始按钮
        self.start_btn = ModernButton("开始抓取", "success")
        self.start_btn.clicked.connect(self.on_start_clicked)
        button_layout.addWidget(self.start_btn)
        
        # 停止按钮
        self.stop_btn = ModernButton("停止抓取", "danger")
        self.stop_btn.clicked.connect(self.on_stop_clicked)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        parent_layout.addLayout(button_layout)
    
    def on_start_clicked(self):
        """开始按钮点击事件"""
        selected_types = []
        
        if self.scrape_user_profile.isChecked():
            selected_types.append("user_profile")
        if self.scrape_followers.isChecked():
            selected_types.append("followers")
        if self.scrape_favorites.isChecked():
            selected_types.append("favorites")
        
        if not selected_types:
            QMessageBox.warning(self, "警告", "请至少选择一种抓取类型！")
            return
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.start_scraping.emit(selected_types)
    
    def on_stop_clicked(self):
        """停止按钮点击事件"""
        self.stop_scraping.emit()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def reset_buttons(self):
        """重置按钮状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)


class ProgressPanel(QWidget):
    """进度显示面板"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("执行状态")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 状态显示
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 8px 12px;
                background-color: #f9fafb;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
            }
        """)
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #10b981;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 统计信息
        self.create_stats_group(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def create_stats_group(self, parent_layout):
        """创建统计信息组"""
        group = QGroupBox("统计信息")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #374151;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # 统计标签
        layout.addWidget(QLabel("已抓取用户:"), 0, 0)
        self.user_count_label = QLabel("0")
        self.user_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.user_count_label, 0, 1)
        
        layout.addWidget(QLabel("已抓取粉丝:"), 1, 0)
        self.follower_count_label = QLabel("0")
        self.follower_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.follower_count_label, 1, 1)
        
        layout.addWidget(QLabel("已抓取视频:"), 2, 0)
        self.video_count_label = QLabel("0")
        self.video_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.video_count_label, 2, 1)
        
        layout.addWidget(QLabel("开始时间:"), 3, 0)
        self.start_time_label = QLabel("-")
        layout.addWidget(self.start_time_label, 3, 1)
        
        parent_layout.addWidget(group)
    
    def update_progress(self, value: int):
        """更新进度"""
        self.progress_bar.setValue(value)
    
    def update_status(self, status: str):
        """更新状态"""
        self.status_label.setText(status)
    
    def update_stats(self, stats: Dict):
        """更新统计信息"""
        if 'user_count' in stats:
            self.user_count_label.setText(str(stats['user_count']))
        if 'follower_count' in stats:
            self.follower_count_label.setText(str(stats['follower_count']))
        if 'video_count' in stats:
            self.video_count_label.setText(str(stats['video_count']))
        if 'start_time' in stats:
            self.start_time_label.setText(stats['start_time'])
    
    def reset_stats(self):
        """重置统计信息"""
        self.user_count_label.setText("0")
        self.follower_count_label.setText("0")
        self.video_count_label.setText("0")
        self.start_time_label.setText("-")
        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")


class LogPanel(QWidget):
    """日志显示面板"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题和控制按钮
        header_layout = QHBoxLayout()
        
        title = QLabel("运行日志")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #1f2937;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # 清除日志按钮
        clear_btn = QPushButton("清除日志")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        clear_btn.clicked.connect(self.clear_logs)
        header_layout.addWidget(clear_btn)
        
        layout.addLayout(header_layout)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumBlockCount(1000)  # 限制最大行数
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #1f2937;
                color: #f9fafb;
                border: 1px solid #374151;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.log_text)
    
    def add_log(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        color = "#f9fafb"  # 默认白色
        if level == "ERROR":
            color = "#ef4444"  # 红色
        elif level == "WARNING":
            color = "#f59e0b"  # 黄色
        elif level == "SUCCESS":
            color = "#10b981"  # 绿色
        
        formatted_message = f'<span style="color: #9ca3af;">[{timestamp}]</span> <span style="color: {color};">[{level}]</span> {message}'
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_logs(self):
        """清除所有日志"""
        self.log_text.clear()


class ResultPanel(QWidget):
    """结果展示面板"""
    
    def __init__(self):
        super().__init__()
        self.current_data = {}
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题和导出按钮
        header_layout = QHBoxLayout()
        
        title = QLabel("结果预览")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #1f2937;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # 导出按钮
        self.export_btn = ModernButton("导出数据", "primary")
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setEnabled(False)
        header_layout.addWidget(self.export_btn)
        
        layout.addLayout(header_layout)
        
        # 结果选项卡
        self.result_tabs = QTabWidget()
        self.result_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f3f4f6;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2563eb;
            }
        """)
        
        # 初始化选项卡
        self.user_table = self.create_result_table()
        self.follower_table = self.create_result_table()
        self.favorite_table = self.create_result_table()
        
        self.result_tabs.addTab(self.user_table, "用户信息")
        self.result_tabs.addTab(self.follower_table, "粉丝列表")
        self.result_tabs.addTab(self.favorite_table, "喜欢列表")
        
        layout.addWidget(self.result_tabs)
    
    def create_result_table(self) -> QTableWidget:
        """创建结果表格"""
        table = QTableWidget()
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.horizontalHeader().setStretchLastSection(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e5e7eb;
                background-color: white;
                selection-background-color: #ddd6fe;
            }
            QHeaderView::section {
                background-color: #f3f4f6;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        return table
    
    def update_data(self, data_info: Dict):
        """更新结果数据"""
        data_type = data_info['type']
        data = data_info['data']
        
        self.current_data[data_type] = data
        
        if data_type == "user_profile":
            self.populate_user_table(data)
        elif data_type == "followers":
            self.populate_follower_table(data)
        elif data_type == "favorites":
            self.populate_favorite_table(data)
        
        self.export_btn.setEnabled(bool(self.current_data))
    
    def populate_user_table(self, user_data: Dict):
        """填充用户信息表格"""
        if not user_data:
            return
        
        self.user_table.setRowCount(1)
        headers = ["昵称", "用户ID", "抖音号", "粉丝数", "关注数", "作品数", "个人简介"]
        self.user_table.setColumnCount(len(headers))
        self.user_table.setHorizontalHeaderLabels(headers)
        
        # 填充数据
        row_data = [
            user_data.get('nickname', ''),
            user_data.get('uid', ''),
            user_data.get('unique_id', ''),
            str(user_data.get('followers', 0)),
            str(user_data.get('following', 0)),
            str(user_data.get('aweme_count', 0)),
            user_data.get('signature', '')
        ]
        
        for col, value in enumerate(row_data):
            item = QTableWidgetItem(str(value))
            self.user_table.setItem(0, col, item)
    
    def populate_follower_table(self, follower_data: List[Dict]):
        """填充粉丝表格"""
        if not follower_data:
            return
        
        self.follower_table.setRowCount(len(follower_data))
        headers = ["昵称", "抖音号", "粉丝数", "关注数", "个人简介"]
        self.follower_table.setColumnCount(len(headers))
        self.follower_table.setHorizontalHeaderLabels(headers)
        
        for row, follower in enumerate(follower_data):
            row_data = [
                follower.get('nickname', ''),
                follower.get('unique_id', ''),
                str(follower.get('follower_count', 0)),
                str(follower.get('following_count', 0)),
                follower.get('signature', '')
            ]
            
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                self.follower_table.setItem(row, col, item)
    
    def populate_favorite_table(self, favorite_data: List[Dict]):
        """填充喜欢列表表格"""
        if not favorite_data:
            return
        
        self.favorite_table.setRowCount(len(favorite_data))
        headers = ["视频ID", "描述", "作者", "点赞数", "评论数", "分享数", "创建时间"]
        self.favorite_table.setColumnCount(len(headers))
        self.favorite_table.setHorizontalHeaderLabels(headers)
        
        for row, video in enumerate(favorite_data):
            row_data = [
                video.get('aweme_id', ''),
                video.get('desc', ''),
                video.get('author', {}).get('nickname', ''),
                str(video.get('statistics', {}).get('digg_count', 0)),
                str(video.get('statistics', {}).get('comment_count', 0)),
                str(video.get('statistics', {}).get('share_count', 0)),
                video.get('create_time', '')
            ]
            
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                self.favorite_table.setItem(row, col, item)
    
    def export_data(self):
        """导出数据"""
        if not self.current_data:
            QMessageBox.warning(self, "警告", "没有数据可导出！")
            return
        
        try:
            export_dir = "data"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            exported_files = []
            
            for data_type, data in self.current_data.items():
                if data:
                    # 导出JSON
                    json_filename = f"{data_type}_{timestamp}.json"
                    json_path = os.path.join(export_dir, json_filename)
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    exported_files.append(json_path)
                    
                    # 导出CSV (仅对列表数据)
                    if isinstance(data, list) and data:
                        csv_filename = f"{data_type}_{timestamp}.csv"
                        csv_path = os.path.join(export_dir, csv_filename)
                        json_to_csv(data, csv_path)
                        exported_files.append(csv_path)
            
            files_text = "\n".join(exported_files)
            QMessageBox.information(self, "导出成功", f"数据已成功导出到:\n{files_text}")
            
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"数据导出失败: {str(e)}")


class DouyinScraperMainWindow(QMainWindow):
    """抖音数据抓取器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.scraper_worker = None
        self.start_time = None
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("抖音数据抓取器 v2.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 应用现代化样式
        self.apply_modern_style()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局：水平分割
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(main_splitter)
        
        # 左侧面板：配置和控制
        left_panel = QWidget()
        left_panel.setFixedWidth(380)
        left_panel.setStyleSheet("""
            QWidget {
                background-color: #f8fafc;
                border-right: 1px solid #e2e8f0;
            }
        """)
        
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(0)
        
        # 配置面板
        self.config_panel = ConfigPanel()
        left_layout.addWidget(self.config_panel, 2)
        
        # 控制面板
        self.control_panel = ControlPanel()
        left_layout.addWidget(self.control_panel, 1)
        
        main_splitter.addWidget(left_panel)
        
        # 右侧面板：结果和日志
        right_panel = QWidget()
        right_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分：进度和结果
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # 进度面板
        self.progress_panel = ProgressPanel()
        self.progress_panel.setFixedWidth(300)
        top_layout.addWidget(self.progress_panel)
        
        # 结果面板
        self.result_panel = ResultPanel()
        top_layout.addWidget(self.result_panel)
        
        right_splitter.addWidget(top_widget)
        
        # 下半部分：日志
        self.log_panel = LogPanel()
        right_splitter.addWidget(self.log_panel)
        
        # 设置分割比例 (3:1)
        right_splitter.setSizes([600, 200])
        
        right_layout = QVBoxLayout(right_panel)
        right_layout.addWidget(right_splitter)
        
        main_splitter.addWidget(right_panel)
        
        # 设置主分割比例
        main_splitter.setSizes([380, 1020])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
            }
            QWidget {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 13px;
            }
            QGroupBox {
                margin-top: 12px;
            }
            QSplitter::handle {
                background-color: #e2e8f0;
            }
            QSplitter::handle:horizontal {
                width: 1px;
            }
            QSplitter::handle:vertical {
                height: 1px;
            }
        """)
    
    def setup_connections(self):
        """设置信号连接"""
        # 控制面板信号
        self.control_panel.start_scraping.connect(self.start_scraping)
        self.control_panel.stop_scraping.connect(self.stop_scraping)
    
    def start_scraping(self, scrape_types: List[str]):
        """开始抓取"""
        # 获取配置
        config = self.config_panel.get_config_dict()
        
        # 验证抖音ID
        if not config['douyin_id']['douyin_id'].strip():
            QMessageBox.warning(self, "警告", "请输入抖音ID！")
            self.control_panel.reset_buttons()
            return
        
        # 重置统计和日志
        self.progress_panel.reset_stats()
        self.log_panel.add_log("开始初始化抓取任务...", "INFO")
        
        # 记录开始时间
        self.start_time = datetime.now()
        self.progress_panel.update_stats({
            'start_time': self.start_time.strftime("%Y-%m-%d %H:%M:%S")
        })
        
        # 创建工作线程
        self.scraper_worker = ScraperWorker(config, scrape_types)
        
        # 连接信号
        self.scraper_worker.progress_updated.connect(self.progress_panel.update_progress)
        self.scraper_worker.status_updated.connect(self.progress_panel.update_status)
        self.scraper_worker.log_message.connect(lambda msg: self.log_panel.add_log(msg, "INFO"))
        self.scraper_worker.data_received.connect(self.result_panel.update_data)
        self.scraper_worker.error_occurred.connect(self.handle_error)
        self.scraper_worker.finished.connect(self.scraping_finished)
        
        # 启动线程
        self.scraper_worker.start()
        
        self.log_panel.add_log(f"抓取任务已启动，类型: {', '.join(scrape_types)}", "SUCCESS")
    
    def stop_scraping(self):
        """停止抓取"""
        if self.scraper_worker and self.scraper_worker.isRunning():
            self.log_panel.add_log("正在停止抓取任务...", "WARNING")
            self.scraper_worker.stop()
            self.progress_panel.update_status("正在停止...")
    
    def handle_error(self, error_msg: str):
        """处理错误"""
        self.log_panel.add_log(f"错误: {error_msg}", "ERROR")
        QMessageBox.critical(self, "抓取错误", error_msg)
        self.control_panel.reset_buttons()
    
    def scraping_finished(self):
        """抓取完成"""
        self.control_panel.reset_buttons()
        
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
            self.log_panel.add_log(f"抓取任务完成，总耗时: {elapsed_str}", "SUCCESS")
        
        self.progress_panel.update_status("完成")
        self.statusBar().showMessage("抓取完成")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("抖音数据抓取器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("DouyinScraper")
    
    # 创建主窗口
    main_window = DouyinScraperMainWindow()
    main_window.show()
    
    sys.exit(app.exec())