"""
工具函数模块
包含数据处理和转换相关的通用函数
"""

import json
import csv
import time
import re
import os
from typing import List, Dict, Union


def json_to_csv(json_data: Union[str, List[Dict]], csv_filename: str = None) -> str:
    """
    将 JSON 数组数据导出到 CSV 文件，自适应处理所有字段。

    Args:
        json_data: JSON 字符串或 Python 列表，包含字典数组
        csv_filename: CSV 文件名，如果为 None 则自动生成

    Returns:
        str: 生成的 CSV 文件名

    Raises:
        ValueError: 当输入数据格式不正确时
    """
    # 解析 JSON 数据
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise ValueError(f"无效的 JSON 格式: {e}")
    elif isinstance(json_data, list):
        data = json_data
    else:
        raise ValueError("输入数据必须是 JSON 字符串或列表")

    # 检查数据是否为空
    if not data:
        raise ValueError("输入数据为空")

    # 检查是否为字典列表
    if not all(isinstance(item, dict) for item in data):
        raise ValueError("JSON 数组中的所有元素必须是对象(字典)")

    # 自动收集所有可能的字段名
    all_fields = set()
    for item in data:
        all_fields.update(item.keys())

    # 按字母顺序排序字段，确保输出一致性
    fieldnames = sorted(all_fields)

    # 生成文件名
    if csv_filename is None:
        csv_filename = get_data_file_path(f"export_data_{int(time.time())}.csv")

    # 写入 CSV 文件
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入数据行
            for item in data:
                # 确保每行都包含所有字段，缺失的字段用空字符串填充
                row = {field: item.get(field, '') for field in fieldnames}
                writer.writerow(row)

        print(f"CSV 文件已生成: {csv_filename}")
        print(f"包含 {len(data)} 行数据，{len(fieldnames)} 个字段")
        print(f"字段列表: {', '.join(fieldnames)}")

        return csv_filename

    except IOError as e:
        raise IOError(f"写入 CSV 文件失败: {e}")


def get_nested_value(data, path, default=''):
    """
    获取嵌套字典中的值，支持点号分隔的路径和数组索引。
    
    Args:
        data: 要查询的数据字典
        path: 路径字符串，如 'user.avatar.url_list[0]'
        default: 默认值，当路径不存在时返回
        
    Returns:
        查询到的值或默认值
        
    Examples:
        >>> data = {'user': {'name': 'test', 'tags': ['a', 'b']}}
        >>> get_nested_value(data, 'user.name')
        'test'
        >>> get_nested_value(data, 'user.tags[0]')
        'a'
    """
    if not path:
        return default
        
    try:
        current = data
        # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
        parts = re.split(r'[\.\[\]]', path)
        parts = [p for p in parts if p]  # 移除空字符串
        
        for part in parts:
            if part.isdigit():
                # 数组索引
                current = current[int(part)]
            else:
                # 字典键
                current = current[part]
                
        return current if current is not None else default
        
    except (KeyError, IndexError, TypeError, AttributeError):
        return default


def ensure_directory(directory: str) -> str:
    """
    确保目录存在，如果不存在则创建。

    Args:
        directory (str): 目录路径

    Returns:
        str: 目录的绝对路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)
        print(f"📁 创建目录: {directory}")
    return os.path.abspath(directory)


def get_data_file_path(filename: str) -> str:
    """
    获取数据文件的完整路径（在 data 目录下）。

    Args:
        filename (str): 文件名

    Returns:
        str: 完整的文件路径
    """
    data_dir = ensure_directory("data")
    return os.path.join(data_dir, filename)


def get_log_file_path(filename: str) -> str:
    """
    获取日志文件的完整路径（在 logs 目录下）。

    Args:
        filename (str): 文件名

    Returns:
        str: 完整的文件路径
    """
    log_dir = ensure_directory("logs")
    return os.path.join(log_dir, filename)


def load_config():
    """
    加载TOML配置，保持向后兼容。

    Returns:
        dict: 配置字典，如果配置文件不存在或解析失败则返回默认配置
    """
    try:
        import tomllib
        with open('config.toml', 'rb') as f:
            return tomllib.load(f)
    except (ImportError, FileNotFoundError):
        # 如果没有tomllib或配置文件，返回默认配置
        return {
            'scraper': {
                'js_timeout': 10,
                'js_retry': 3,
                'sleep_between_tries': 0.8,
                'sleep_between_pages': 1.5,
                'video_retry_base_delay': 0.8,
                'video_retry_extra_delay_min': 0.5,
                'video_retry_extra_delay_max': 0.8
            },
            'limits': {
                'max_follower_count': 30,
                'max_favorite_count': 30
            },
            'data_quality': {
                'listener_cleanup_timeout': 1.0,
                'listener_cleanup_rounds': 3,
                'strict_data_validation': True,
                'enable_deduplication': True
            },
            'export': {
                'follower_json': True,
                'follower_csv': True,
                'favorite_json': True,
                'favorite_csv': True
            }
        }
