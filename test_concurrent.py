#!/usr/bin/env python3
"""
并发功能测试脚本
用于验证多标签页并发获取视频信息的功能是否正常工作
"""

import time
import logging
from douyin import DouyinScraper
from logger import setup_logger
from utils import load_config

def test_concurrent_mode():
    """测试并发模式"""
    logger = setup_logger("ConcurrentTest", logging.INFO)
    logger.info("=" * 60)
    logger.info("开始测试并发模式")
    logger.info("=" * 60)
    
    # 加载配置
    config = load_config()
    douyin_id = config.get('douyin_id', {}).get('douyin_id', '')
    
    if not douyin_id:
        logger.error("配置文件中未找到douyin_id，请先配置")
        return False
        
    try:
        # 创建抓取器实例
        scraper = DouyinScraper()
        
        # 检查并发模式是否启用
        if not scraper.ENABLE_CONCURRENT_MODE:
            logger.warning("并发模式未启用，请检查配置文件")
            return False
            
        logger.info(f"并发配置 - 最大标签页数: {scraper.MAX_CONCURRENT_TABS}")
        logger.info(f"标签页初始化延迟: {scraper.TAB_INIT_DELAY}s")
        
        # 获取用户sec_uid
        logger.info(f"获取用户信息 - 抖音ID: {douyin_id}")
        sec_uid = scraper.fetch_sec_uid(douyin_id)
        logger.info(f"获取到sec_uid: {sec_uid}")
        
        # 测试小量数据的并发获取
        test_count = 5  # 测试5个视频
        logger.info(f"开始并发测试 - 测试数量: {test_count}")
        
        start_time = time.time()
        
        # 调用并发获取方法
        favorites = scraper.fetch_favorites_concurrent(
            sec_uid=sec_uid,
            max_items=test_count
        )
        
        elapsed_time = time.time() - start_time
        
        # 输出结果
        logger.info("=" * 60)
        logger.info("测试结果:")
        logger.info(f"获取视频数量: {len(favorites)}")
        logger.info(f"总耗时: {elapsed_time:.2f}s")
        if len(favorites) > 0:
            logger.info(f"平均每个视频耗时: {elapsed_time/len(favorites):.2f}s")
        logger.info("=" * 60)
        
        # 显示前几个视频的基本信息
        for i, video in enumerate(favorites[:3], 1):
            logger.info(f"视频{i}: {video.get('视频id', 'N/A')} - {video.get('视频描述', 'N/A')[:50]}...")
            
        return len(favorites) > 0
        
    except Exception as e:
        logger.error(f"并发测试失败: {e}", exc_info=True)
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_fallback_mode():
    """测试回退模式（串行模式）"""
    logger = setup_logger("FallbackTest", logging.INFO)
    logger.info("=" * 60)
    logger.info("开始测试回退模式（串行模式）")
    logger.info("=" * 60)
    
    # 加载配置
    config = load_config()
    douyin_id = config.get('douyin_id', {}).get('douyin_id', '')
    
    try:
        # 创建抓取器实例
        scraper = DouyinScraper()
        
        # 临时禁用并发模式
        scraper.ENABLE_CONCURRENT_MODE = False
        logger.info("已临时禁用并发模式，测试回退逻辑")
        
        # 获取用户sec_uid
        sec_uid = scraper.fetch_sec_uid(douyin_id)
        
        # 测试回退到串行模式
        test_count = 3
        logger.info(f"开始回退模式测试 - 测试数量: {test_count}")
        
        start_time = time.time()
        
        # 调用并发方法（应该自动回退到串行模式）
        favorites = scraper.fetch_favorites_concurrent(
            sec_uid=sec_uid,
            max_items=test_count
        )
        
        elapsed_time = time.time() - start_time
        
        # 输出结果
        logger.info("回退模式测试结果:")
        logger.info(f"获取视频数量: {len(favorites)}")
        logger.info(f"总耗时: {elapsed_time:.2f}s")
        
        return len(favorites) > 0
        
    except Exception as e:
        logger.error(f"回退模式测试失败: {e}", exc_info=True)
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

if __name__ == "__main__":
    print("抖音并发功能测试")
    print("注意：测试过程中请不要手动操作浏览器")
    print()
    
    # 测试并发模式
    concurrent_success = test_concurrent_mode()
    print(f"并发模式测试: {'通过' if concurrent_success else '失败'}")
    
    time.sleep(2)  # 间隔一下
    
    # 测试回退模式
    fallback_success = test_fallback_mode()
    print(f"回退模式测试: {'通过' if fallback_success else '失败'}")
    
    print()
    if concurrent_success and fallback_success:
        print("所有测试通过！并发功能已准备就绪")
    else:
        print("部分测试失败，请检查日志信息")