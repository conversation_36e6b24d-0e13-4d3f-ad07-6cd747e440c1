#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频信息获取性能优化测试脚本

测试前三个阶段的优化效果：
- 阶段一：监听器复用优化
- 阶段二：队列清理机制优化  
- 阶段三：加载模式优化验证

使用方法：
python test_optimization.py
"""

import time
import json
from douyin import DouyinScraper
from logger import setup_logger

def test_performance_comparison():
    """性能对比测试"""
    logger = setup_logger("PerformanceTest")
    
    # 测试用的用户ID（请替换为实际的用户ID）
    test_sec_uid = "MS4wLjABAAAA_example_user_id"  # 请替换为实际的用户ID
    test_video_count = 5  # 测试视频数量，建议先用小数量测试
    
    logger.info("=" * 60)
    logger.info("抖音视频信息获取性能优化测试")
    logger.info("=" * 60)
    
    scraper = DouyinScraper()
    
    try:
        # 测试阶段一：监听器复用优化
        logger.info("\n🚀 测试阶段一：监听器复用优化")
        logger.info("-" * 40)
        
        start_time = time.time()
        stage1_results = scraper.fetch_favorites_stage1_optimized(
            sec_uid=test_sec_uid, 
            max_items=test_video_count
        )
        stage1_time = time.time() - start_time
        
        logger.info(f"阶段一完成 - 获取 {len(stage1_results)} 个视频，耗时: {stage1_time:.2f}s")
        logger.info(f"平均每视频耗时: {stage1_time/max(len(stage1_results), 1):.2f}s")
        
        # 等待一段时间，避免请求过于频繁
        time.sleep(5)
        
        # 测试阶段二：队列清理机制优化
        logger.info("\n🔧 测试阶段二：队列清理机制优化")
        logger.info("-" * 40)
        
        start_time = time.time()
        stage2_results = scraper.fetch_favorites_stage2_optimized(
            sec_uid=test_sec_uid, 
            max_items=test_video_count
        )
        stage2_time = time.time() - start_time
        
        logger.info(f"阶段二完成 - 获取 {len(stage2_results)} 个视频，耗时: {stage2_time:.2f}s")
        logger.info(f"平均每视频耗时: {stage2_time/max(len(stage2_results), 1):.2f}s")
        
        # 等待一段时间
        time.sleep(5)
        
        # 测试阶段三：完整优化
        logger.info("\n⚡ 测试阶段三：完整优化（监听器复用 + 队列清理 + 加载模式）")
        logger.info("-" * 40)
        
        start_time = time.time()
        stage3_results = scraper.fetch_favorites_stage3_optimized(
            sec_uid=test_sec_uid, 
            max_items=test_video_count
        )
        stage3_time = time.time() - start_time
        
        logger.info(f"阶段三完成 - 获取 {len(stage3_results)} 个视频，耗时: {stage3_time:.2f}s")
        logger.info(f"平均每视频耗时: {stage3_time/max(len(stage3_results), 1):.2f}s")
        
        # 性能对比分析
        logger.info("\n📊 性能对比分析")
        logger.info("=" * 60)
        
        results = [
            ("阶段一（监听器复用）", len(stage1_results), stage1_time),
            ("阶段二（+ 队列清理）", len(stage2_results), stage2_time),
            ("阶段三（完整优化）", len(stage3_results), stage3_time)
        ]
        
        logger.info(f"{'阶段':<20} {'视频数':<8} {'总耗时(s)':<12} {'平均耗时(s)':<12}")
        logger.info("-" * 60)
        
        for stage_name, video_count, total_time in results:
            avg_time = total_time / max(video_count, 1)
            logger.info(f"{stage_name:<20} {video_count:<8} {total_time:<12.2f} {avg_time:<12.2f}")
        
        # 计算性能提升
        if len(results) >= 2:
            baseline_time = results[0][2]  # 阶段一作为基准
            for i, (stage_name, video_count, total_time) in enumerate(results[1:], 1):
                if baseline_time > 0:
                    improvement = ((baseline_time - total_time) / baseline_time) * 100
                    logger.info(f"{stage_name} 相比阶段一性能提升: {improvement:.1f}%")
        
        # 保存测试结果
        test_results = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_video_count": test_video_count,
            "results": {
                "stage1": {
                    "video_count": len(stage1_results),
                    "total_time": stage1_time,
                    "avg_time": stage1_time / max(len(stage1_results), 1)
                },
                "stage2": {
                    "video_count": len(stage2_results),
                    "total_time": stage2_time,
                    "avg_time": stage2_time / max(len(stage2_results), 1)
                },
                "stage3": {
                    "video_count": len(stage3_results),
                    "total_time": stage3_time,
                    "avg_time": stage3_time / max(len(stage3_results), 1)
                }
            }
        }
        
        with open(f"performance_test_results_{int(time.time())}.json", 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n✅ 测试完成！结果已保存到 performance_test_results_{int(time.time())}.json")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        raise
    finally:
        scraper.close()

def test_single_stage(stage_num: int = 3):
    """测试单个阶段的优化效果"""
    logger = setup_logger("SingleStageTest")
    
    test_sec_uid = "MS4wLjABAAAA_example_user_id"  # 请替换为实际的用户ID
    test_video_count = 3
    
    scraper = DouyinScraper()
    
    try:
        if stage_num == 1:
            logger.info("测试阶段一：监听器复用优化")
            results = scraper.fetch_favorites_stage1_optimized(test_sec_uid, test_video_count)
        elif stage_num == 2:
            logger.info("测试阶段二：队列清理机制优化")
            results = scraper.fetch_favorites_stage2_optimized(test_sec_uid, test_video_count)
        elif stage_num == 3:
            logger.info("测试阶段三：完整优化")
            results = scraper.fetch_favorites_stage3_optimized(test_sec_uid, test_video_count)
        else:
            raise ValueError("stage_num 必须是 1, 2, 或 3")
        
        logger.info(f"测试完成 - 获取 {len(results)} 个视频")
        
        # 显示部分结果
        for i, video in enumerate(results[:2]):  # 只显示前2个视频的信息
            logger.info(f"视频 {i+1}: {video.get('视频id', 'N/A')} - {video.get('视频描述', 'N/A')[:50]}...")
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise
    finally:
        scraper.close()

if __name__ == "__main__":
    print("抖音视频信息获取性能优化测试")
    print("请确保已经正确配置了测试用的用户ID")
    print()
    
    choice = input("选择测试模式:\n1. 完整性能对比测试\n2. 单阶段测试\n请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        print("开始完整性能对比测试...")
        test_performance_comparison()
    elif choice == "2":
        stage = input("请输入要测试的阶段 (1, 2, 或 3): ").strip()
        try:
            stage_num = int(stage)
            if stage_num in [1, 2, 3]:
                print(f"开始测试阶段 {stage_num}...")
                test_single_stage(stage_num)
            else:
                print("无效的阶段号，请输入 1, 2, 或 3")
        except ValueError:
            print("请输入有效的数字")
    else:
        print("无效的选择")
