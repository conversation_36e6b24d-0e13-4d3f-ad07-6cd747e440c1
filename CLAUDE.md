# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

基于 DrissionPage 4.x 的高性能抖音数据抓取器，采用模块化架构和多阶段性能优化策略，支持获取用户基本信息、粉丝列表和点赞视频等数据。

## 常用开发命令

```bash
# 运行主程序
python main.py

# 安装依赖
pip install DrissionPage jmespath

# 性能测试
python test_optimization.py

# 并发功能测试  
python test_concurrent.py

# 单独测试某个功能模块
python -c "from douyin import DouyinScraper; scraper = DouyinScraper(); print('模块导入成功')"
```

## 项目架构

### 核心模块
- **main.py**: 程序入口，流程控制和配置加载
- **douyin.py**: DouyinScraper 核心业务逻辑，包含所有抓取功能
- **utils.py**: 工具函数和配置管理，数据处理和转换
- **logger.py**: 日志系统模块，统一日志管理
- **config.toml**: 外置配置文件，所有参数可配置

### 目录结构
```
dp_douyin/
├── data/                 # 数据导出目录（自动创建）
├── logs/                 # 日志文件目录（自动创建）
├── docs/                 # 项目文档
├── test/                 # 测试脚本
├── PLANNING.md           # 项目规划文档
└── TASK.md              # 任务记录
```

## 核心配置 (config.toml)

```toml
[douyin_id]
douyin_id = "96967475948"  # 目标用户抖音ID

[scraper]
js_timeout = 10            # JS执行超时时间（秒）
js_retry = 3               # 重试次数
sleep_between_tries = 0.8  # 重试间隔（秒）
sleep_between_pages = 1.5  # 防风控翻页间隔（秒）

[limits]
max_follower_count = 30    # 最大粉丝抓取数量
max_favorite_count = 20    # 最大点赞视频抓取数量

[optimization]
optimization_stage = 3     # 性能优化阶段（1-3）
enable_performance_stats = true
enable_debug_logging = false

[concurrent]
enable_concurrent_mode = true  # 是否启用并发模式
max_concurrent_tabs = 5        # 最大并发标签页数
```

## DrissionPage 4.x 最佳实践

### 监听器复用优化
```python
# ⭐ 核心优化：一次设置，批量复用
self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")
for video_id in video_list:
    packet = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
```

### 性能优化策略
- **监听器复用**: 避免频繁的 `listen.clear() + listen.start()`
- **使用推荐API**: 优先使用 `listen.wait()` 而非 `listen.steps()`
- **队列清理机制**: 智能清理监听器队列，防止数据包混乱
- **加载模式优化**: 使用 `set_load_mode('none')` 提升性能

## 关键功能模块

### DouyinScraper 核心方法
- `fetch_sec_uid(douyin_id)`: 通过抖音ID获取sec_uid
- `fetch_user_profile(sec_uid)`: 获取用户基本信息  
- `fetch_followers(sec_uid, max_items)`: 抓取粉丝列表
- `fetch_favorites(sec_uid, max_items)`: 获取点赞视频列表
- `fetch_video_info(video_id)`: 获取单个视频详细信息

### 数据处理工具 (utils.py)
- `json_to_csv()`: JSON数据转CSV文件
- `load_config()`: 加载配置文件
- `get_data_file_path()`: 获取数据文件路径

## 环境要求

- **Python**: 3.8+
- **系统**: Windows + PowerShell 7  
- **依赖**: `pip install DrissionPage jmespath`
- **浏览器**: Chrome浏览器

## 开发注意事项

### 模块化架构特点
- 所有配置通过 `config.toml` 管理，支持无配置文件运行
- 采用三阶段性能优化架构，可配置优化级别
- 支持并发模式，可启用多标签页同时处理
- 自动目录管理，数据文件保存到 `data/`，日志保存到 `logs/`

### 性能优化核心
1. **监听器复用**: 避免重复设置监听器，显著提升性能
2. **队列清理机制**: 防止数据包混乱，提高数据准确性
3. **智能重试**: 区分技术错误和风控验证，减少不必要的用户干预
4. **并发处理**: 支持多标签页并发获取视频信息

### 风控处理策略
- 内置防风控间隔设置和智能重试机制
- 支持滑块验证检测和用户交互处理
- 可配置的延迟和重试参数，适应不同网络环境

## 重要提醒

1. **配置优先**: 优先通过 `config.toml` 修改参数，而非直接修改代码
2. **测试先行**: 使用测试脚本验证功能，避免在生产环境直接调试
3. **文档同步**: 新增功能或修改配置时，记得更新 `TASK.md` 和相关文档
4. **数据用途**: 仅用于学习研究，遵守相关法律法规