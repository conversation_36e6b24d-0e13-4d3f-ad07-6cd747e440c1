2025-07-28 23:49:16 - Main - INFO - ============================================================
2025-07-28 23:49:16 - Main - INFO - 抖音数据抓取程序启动
2025-07-28 23:49:16 - Main - INFO - ============================================================
2025-07-28 23:49:16 - Main - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-28 23:49:16 - Main - INFO - 导出选项 - JSON: True, CSV: True
2025-07-28 23:49:16 - Main - INFO - 开始处理用户 - 抖音ID: 96967475948
2025-07-28 23:49:16 - <PERSON><PERSON>inScraper - INFO - 开始获取用户 sec_uid - 抖音ID: 96967475948
2025-07-28 23:49:19 - DouyinScraper - INFO - 成功获取 sec_uid: MS4wLjABAAAA8LvNkjqN2KMfdvLEmYvncJbNDlLxPG6JVb7a525CCd6AoM6RX-8KymWDtmV8gd3V - 抖音ID: 96967475948
2025-07-28 23:49:19 - Main - INFO - 开始获取用户基本信息
2025-07-28 23:49:21 - Main - INFO - 用户基本信息获取完成
2025-07-28 23:49:24 - DouyinScraper - WARNING - 首包响应中无 followers 字段, 重试: 1
2025-07-28 23:49:24 - Main - ERROR - 程序执行出现异常: cannot access local variable 'time' where it is not associated with a value
Traceback (most recent call last):
  File "d:\Desk\douyin\dp_douyin\main.py", line 64, in main
    followers = scraper.fetch_followers(
        sec_uid=sec_uid,
        max_items=Max_follower_count,     # 按需调整
        page_count=20
    )
  File "d:\Desk\douyin\dp_douyin\douyin.py", line 352, in fetch_followers
    time.sleep(self.SLEEP_BETWEEN_TRIES)
    ^^^^
UnboundLocalError: cannot access local variable 'time' where it is not associated with a value
2025-07-28 23:49:24 - Main - INFO - ============================================================
2025-07-28 23:49:24 - Main - INFO - 抖音数据抓取程序结束
2025-07-28 23:49:24 - Main - INFO - ============================================================
